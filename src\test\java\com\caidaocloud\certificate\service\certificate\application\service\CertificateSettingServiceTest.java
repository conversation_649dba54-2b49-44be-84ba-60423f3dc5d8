package com.caidaocloud.certificate.service.certificate.application.service;

import com.caidaocloud.certificate.service.certificate.domain.entity.CertificateSettingDo;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CertificateSettingModelDto;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.SettingDto;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * CertificateSettingService 测试类
 * 验证重构后的selectList方法拆分功能
 */
@SpringBootTest
@ActiveProfiles("test")
public class CertificateSettingServiceTest {

    @Resource
    private CertificateSettingService certificateSettingService;

    @Test
    public void testSetMethodReturnsDto() {
        // 测试set方法返回CertificateSettingModelDto列表
        String typeCode = "msg"; // 使用一个测试用的typeCode

        try {
            List<CertificateSettingModelDto> result = certificateSettingService.set(typeCode);

            // 验证返回类型正确
            assertNotNull(result, "结果不应为null");
            assertTrue(result instanceof List, "结果应该是List类型");

            // 如果有结果，验证DTO结构
            if (!result.isEmpty()) {
                CertificateSettingModelDto firstDto = result.get(0);
                assertNotNull(firstDto, "DTO不应为null");

                // 验证DTO包含必要的字段
                assertNotNull(firstDto.getName(), "name字段不应为null");
                assertNotNull(firstDto.getIdentifier(), "identifier字段不应为null");
                assertNotNull(firstDto.getProperties(), "properties字段不应为null");

                System.out.println("测试通过：成功返回CertificateSettingModelDto列表");
                System.out.println("第一个DTO - Name: " + firstDto.getName() +
                        ", Identifier: " + firstDto.getIdentifier() +
                        ", Properties count: " + firstDto.getProperties().size());
            } else {
                System.out.println("测试通过：返回空列表（可能是因为测试环境配置）");
            }

        } catch (Exception e) {
            fail("测试失败：" + e.getMessage());
        }
    }

    @Test
    public void testSelectSettingDtoList() {
        // 测试selectSettingDtoList方法返回SettingDto列表
        CertificateSettingDo dto = new CertificateSettingDo();
        dto.setTypeCode("qua"); // 非msg类型

        try {
            List<SettingDto> result = certificateSettingService.selectSettingDtoList(dto);

            // 验证返回类型正确
            assertNotNull(result, "结果不应为null");
            assertTrue(result instanceof List, "结果应该是List类型");

            System.out.println("测试通过：selectSettingDtoList返回SettingDto列表，数量: " + result.size());

        } catch (Exception e) {
            fail("selectSettingDtoList测试失败：" + e.getMessage());
        }
    }

    @Test
    public void testSelectCertificateSettingDoList() {
        // 测试selectCertificateSettingDoList方法返回CertificateSettingDo列表
        CertificateSettingDo dto = new CertificateSettingDo();
        dto.setTypeCode("msg"); // msg类型

        try {
            List<CertificateSettingDo> result = certificateSettingService.selectMsgSettingDoList(dto);

            // 验证返回类型正确
            assertNotNull(result, "结果不应为null");
            assertTrue(result instanceof List, "结果应该是List类型");

            System.out.println("测试通过：selectCertificateSettingDoList返回CertificateSettingDo列表，数量: " + result.size());

        } catch (Exception e) {
            fail("selectCertificateSettingDoList测试失败：" + e.getMessage());
        }
    }

    @Test
    public void testSelectListCompatibility() {
        // 测试原selectList方法的兼容性
        CertificateSettingDo msgDto = new CertificateSettingDo();
        msgDto.setTypeCode("msg");

        CertificateSettingDo nonMsgDto = new CertificateSettingDo();
        nonMsgDto.setTypeCode("qua");

        try {
            // 测试msg类型调用
            Object msgResult = certificateSettingService.selectList(msgDto);
            assertNotNull(msgResult, "msg类型结果不应为null");
            assertTrue(msgResult instanceof List, "msg类型结果应该是List");

            // 测试非msg类型调用
            Object nonMsgResult = certificateSettingService.selectList(nonMsgDto);
            assertNotNull(nonMsgResult, "非msg类型结果不应为null");
            assertTrue(nonMsgResult instanceof List, "非msg类型结果应该是List");

            System.out.println("测试通过：selectList方法保持向后兼容性");

        } catch (Exception e) {
            fail("selectList兼容性测试失败：" + e.getMessage());
        }
    }
}
