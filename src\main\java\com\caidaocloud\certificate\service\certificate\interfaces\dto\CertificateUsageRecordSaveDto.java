package com.caidaocloud.certificate.service.certificate.interfaces.dto;

import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "证书使用情况保存dto")
@Data
public class CertificateUsageRecordSaveDto {
	private String bid;
	@ApiModelProperty(notes = "证书类别bid")
	private String certificate;
	@ApiModelProperty(notes = "登记类别")
	private String type;
	@ApiModelProperty(notes = "证书登记开始日期（毫秒时间戳）")
	private Long startDate;
	@ApiModelProperty(notes = "证书登记结束日期（毫秒时间戳）")
	private Long endDate;
	@ApiModelProperty(notes = "项目id")
	private String projectId;
	@ApiModelProperty(notes = "证书状态")
	private List<String> empCertificate;
	@ApiModelProperty(notes = "备注")
	private String remark;
}

