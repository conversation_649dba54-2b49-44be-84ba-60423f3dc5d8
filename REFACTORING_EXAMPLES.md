# 重构前后代码对比示例

## 1. CertificateExpireTask 重构对比

### 重构前
```java
// 需要强制类型转换，存在ClassCastException风险
CertificateSettingDo data = new CertificateSettingDo();
data.setTypeCode("msg");
List<CertificateSettingDo> settingDtos = (List<CertificateSettingDo>)certificateSettingService.selectList(data);
```

### 重构后
```java
// 类型安全，无需强制转换
CertificateSettingDo data = new CertificateSettingDo();
data.setTypeCode("msg");
List<CertificateSettingDo> settingDtos = certificateSettingService.selectCertificateSettingDoList(data);
```

**优势**:
- ✅ 消除了强制类型转换
- ✅ 编译时类型检查
- ✅ 方法名明确表达返回类型

---

## 2. QualifiedPerService 重构对比

### 重构前
```java
// 需要通过JSON序列化/反序列化来获取正确类型
CertificateSettingDo certificateSettingDo = new CertificateSettingDo();
certificateSettingDo.setTypeCode("qua");
Object d = certificateSettingService.selectList(certificateSettingDo);
List<SettingDto> settingDtos = FastjsonUtil.toArrayList(FastjsonUtil.toJson(d), SettingDto.class);
```

### 重构后
```java
// 直接获取强类型结果，无需序列化转换
CertificateSettingDo certificateSettingDo = new CertificateSettingDo();
certificateSettingDo.setTypeCode("qua");
List<SettingDto> settingDtos = certificateSettingService.selectSettingDtoList(certificateSettingDo);
```

**优势**:
- ✅ 避免了JSON序列化/反序列化的性能开销
- ✅ 减少了代码复杂度
- ✅ 消除了序列化过程中可能的数据丢失风险

---

## 3. CertificateSettingService 内部重构对比

### 重构前
```java
public Object selectList(CertificateSettingDo dto) {
    if (!dto.getTypeCode().equals("msg")) {
        // 复杂的业务逻辑混在一个方法中
        List<CertificateSettingDo> settingDos = certificateSettingDo.selectList(dto);
        List<SettingDto> dtos = coverSetDto(settingDos).stream()
                .sorted(Comparator.comparing(SettingDto::getSort))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(dtos)) {
            CertificateSetType type = CertificateSetType.getByCode(dto.getTypeCode());
            List<SettingDto> list = Sequences.sequence(buildTypeModel(LangUtil.getLocale(), type))
                    .flatMap(CertificateSettingModelDto::getProperties).toList();
            return Sequences.sequence(type.defaultProperty)
                    .map(p -> Sequences.sequence(list).find(s -> s.getProperty().equals(p)).getOrNull()).toList();
        }
        return dtos;
    }
    return certificateSettingDo.selectList(dto);
}
```

### 重构后
```java
// 主方法保持简洁，职责分离
public Object selectList(CertificateSettingDo dto) {
    if (!dto.getTypeCode().equals("msg")) {
        return selectSettingDtoList(dto);
    }
    return selectCertificateSettingDoList(dto);
}

// 专门处理SettingDto的方法
public List<SettingDto> selectSettingDtoList(CertificateSettingDo dto) {
    List<CertificateSettingDo> settingDos = certificateSettingDo.selectList(dto);
    List<SettingDto> dtos = coverSetDto(settingDos).stream()
            .sorted(Comparator.comparing(SettingDto::getSort))
            .collect(Collectors.toList());
    if (CollectionUtils.isEmpty(dtos)) {
        CertificateSetType type = CertificateSetType.getByCode(dto.getTypeCode());
        List<SettingDto> list = Sequences.sequence(buildTypeModel(LangUtil.getLocale(), type))
                .flatMap(CertificateSettingModelDto::getProperties).toList();
        return Sequences.sequence(type.defaultProperty)
                .map(p -> Sequences.sequence(list).find(s -> s.getProperty().equals(p)).getOrNull()).toList();
    }
    return dtos;
}

// 专门处理CertificateSettingDo的方法
public List<CertificateSettingDo> selectCertificateSettingDoList(CertificateSettingDo dto) {
    return certificateSettingDo.selectList(dto);
}
```

**优势**:
- ✅ 单一职责原则：每个方法只处理一种业务场景
- ✅ 类型安全：返回类型明确
- ✅ 易于维护：业务逻辑分离，便于独立修改
- ✅ 向后兼容：原方法保留，不影响现有调用

---

## 4. 调用方式对比总结

| 场景 | 重构前 | 重构后 | 改进点 |
|------|--------|--------|--------|
| **明确返回CertificateSettingDo** | `(List<CertificateSettingDo>)service.selectList(dto)` | `service.selectCertificateSettingDoList(dto)` | 无需强制转换 |
| **明确返回SettingDto** | `FastjsonUtil.toArrayList(FastjsonUtil.toJson(service.selectList(dto)), SettingDto.class)` | `service.selectSettingDtoList(dto)` | 避免序列化开销 |
| **类型不确定** | `Object result = service.selectList(dto)` | `Object result = service.selectList(dto)` | 保持兼容性 |

---

## 5. 性能对比

### 重构前性能问题
```java
// QualifiedPerService 中的性能瓶颈
Object d = certificateSettingService.selectList(certificateSettingDo);
List<SettingDto> settingDtos = FastjsonUtil.toArrayList(FastjsonUtil.toJson(d), SettingDto.class);
```
**性能开销**:
1. 对象 → JSON字符串序列化
2. JSON字符串 → List<SettingDto>反序列化
3. 反射操作创建对象

### 重构后性能优化
```java
// 直接获取强类型结果
List<SettingDto> settingDtos = certificateSettingService.selectSettingDtoList(certificateSettingDo);
```
**性能提升**:
1. ✅ 消除JSON序列化/反序列化
2. ✅ 减少反射操作
3. ✅ 降低内存使用

---

## 6. 错误处理对比

### 重构前潜在问题
```java
// 可能的运行时异常
List<CertificateSettingDo> settingDtos = (List<CertificateSettingDo>)certificateSettingService.selectList(data);
// 如果实际返回的是List<SettingDto>，会抛出ClassCastException
```

### 重构后错误预防
```java
// 编译时类型检查，避免运行时异常
List<CertificateSettingDo> settingDtos = certificateSettingService.selectCertificateSettingDoList(data);
// 编译器保证返回类型正确
```

**安全性提升**:
- ✅ 编译时发现类型错误
- ✅ 避免ClassCastException
- ✅ 提高代码健壮性
