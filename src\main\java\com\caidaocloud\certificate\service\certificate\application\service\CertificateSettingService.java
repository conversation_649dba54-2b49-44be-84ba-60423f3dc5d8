package com.caidaocloud.certificate.service.certificate.application.service;

import com.caidaocloud.certificate.service.certificate.domain.base.util.LangUtil;
import com.caidaocloud.certificate.service.certificate.domain.entity.CertificateSettingDo;
import com.caidaocloud.certificate.service.certificate.domain.enums.CertificateSetType;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CeritificateSettingDto;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CertificateSetSortDto;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CertificateSettingGetResultDto;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CertificateSettingModelDto;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.SettingDto;
import com.caidaocloud.hrpaas.metadata.sdk.service.MetadataOperatorService;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataPropertyVo;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataVo;
import com.caidaocloud.util.BeanUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.ObjectUtil;
import com.google.common.collect.Lists;
import com.googlecode.totallylazy.Sequences;
import com.googlecode.totallylazy.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.jetbrains.annotations.NotNull;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * CreateName: Mr.Li
 * CreateTime: 2024/5/20 16:27
 **/
@Service
@Slf4j
public class CertificateSettingService {

    @Resource
    private CertificateSettingDo certificateSettingDo;
    @Resource
    private MetadataOperatorService metadataOperatorService;
    // 员工任职信息
    // private static final Set<String> OCCUPY_EMP_PROPERTY = Sets.set("name",
    // "hireDate", "organizeTxt",
    // "postTxt","workno","jobGrade$startGradeName","jobTxt");
    // 员工必填信息
    private static final Set<String> OCCUPY_MUST_PROPERTY = Sets.set("name", "organizeTxt", "postTxt", "workno");

    // 员工个人信息
    // private static final Set<String> PRIVATE_EMP_PROPERTY = Sets.set("sex",
    // "email", "permanentAddress");
    public Object selectList(CertificateSettingDo dto) {
        if (!dto.getTypeCode().equals("msg")) {
            return selectSettingDtoList(dto);
        }
        return selectMsgSettingDoList(dto);
    }

    /**
     * 获取SettingDto集合 - 用于非msg类型的查询
     * 
     * @param dto 查询条件
     * @return SettingDto集合
     */
    public List<SettingDto> selectSettingDtoList(CertificateSettingDo dto) {
        List<CertificateSettingDo> settingDos = certificateSettingDo.selectList(dto);
        List<SettingDto> dtos = coverSetDto(settingDos).stream().sorted(Comparator.comparingInt(set-> Integer.parseInt(set.getSort())))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(dtos)) {
            CertificateSetType type = CertificateSetType.getByCode(dto.getTypeCode());
            List<SettingDto> list = Sequences.sequence(buildTypeModel(LangUtil.getLocale(), type))
                    .flatMap(CertificateSettingModelDto::getProperties).toList();
            return Sequences.sequence(type.defaultProperty)
                    .map(p -> Sequences.sequence(list).find(s -> s.getProperty().equals(p)).getOrNull()).toList();
        }
        return dtos;
    }

    /**
     * 获取CertificateSettingDo集合 - 用于msg类型的查询
     * 
     * @param dto 查询条件
     * @return CertificateSettingDo集合
     */
    public List<CertificateSettingDo> selectMsgSettingDoList(CertificateSettingDo dto) {
        return certificateSettingDo.selectList(dto);
    }

    public String add(CertificateSettingDo dto) {
        return certificateSettingDo.save(dto);
    }

    public void del(CertificateSettingDo dto) {
        certificateSettingDo.delete(dto);
    }

    public void updata(CertificateSettingDo dto) {
        certificateSettingDo.update(dto);
    }

    public void saveBatch(CeritificateSettingDto dto) {
        certificateSettingDo.saveBatch(dto);
    }

    public List<CertificateSettingGetResultDto> get(CertificateSettingDo dto) {
        List<CertificateSettingDo> list = certificateSettingDo.selectList(dto);
        Map<String, List<CertificateSettingDo>> collect = null;
        if (CollectionUtils.isNotEmpty(list)) {
            collect = list.stream().collect(Collectors.groupingBy(CertificateSettingDo::getMsgBid));
            List<CertificateSettingGetResultDto> result = new ArrayList<>();
            for (Map.Entry<String, List<CertificateSettingDo>> entry : collect.entrySet()) {
                CertificateSettingGetResultDto resultDto = new CertificateSettingGetResultDto();
                List<SettingDto> dtos = coverSetDto(entry.getValue());
                resultDto.setModel(entry.getKey());
                resultDto.setProperties(dtos);
                result.add(resultDto);
            }
            return result;
        }

        return new ArrayList<>();
    }

    private List<SettingDto> coverSetDto(List<CertificateSettingDo> value) {
        List<SettingDto> dtos = new ArrayList<>();
        value.forEach(t -> {
            SettingDto settingDto = new SettingDto();
            settingDto.setEntity(t.getMsgBid());
            settingDto.setBid(t.getBid());
            settingDto.setProperty(t.getMsgName());
            settingDto.setName(t.getCertificateName());
            settingDto.setRemake(t.getRemake());
            settingDto.setSort(t.getCertificateBid());
            dtos.add(settingDto);
        });
        return dtos;
    }

    public List<CertificateSettingModelDto> set(String typeCode) {
        Locale locale = LangUtil.getLocale();
        CertificateSetType type = CertificateSetType.getByCode(typeCode);

        List<CertificateSettingModelDto> result = buildTypeModel(locale, type);

        // MetadataVo occupyMetadata = getMetadata("entity.hr.EmpWorkInfo");
        // List<MetadataPropertyVo> occupyList = getMetadataProperty(occupyMetadata);
        // CertificateSettingResultDto occupyDto = createResultDto(locale, occupyList,
        // "员工任职信息", "entity.hr.EmpWorkInfo",
        // occupyMetadata.getCustomProperties(), Collections.emptySet());
        // result.add(occupyDto);
        //
        // MetadataVo privateMetadata = getMetadata("entity.hr.EmpPrivateInfo");
        // List<MetadataPropertyVo> privateList = getMetadataProperty(privateMetadata);
        // CertificateSettingResultDto privateDto = createResultDto(locale, privateList,
        // "员工个人信息",
        // "entity.hr.EmpPrivateInfo", privateMetadata.getCustomProperties(),
        // Collections.emptySet());
        // result.add(privateDto);
        return result;
    }

    @NotNull
    private List<CertificateSettingModelDto> buildTypeModel(Locale locale, CertificateSetType type) {
        List<CertificateSettingModelDto> result = new ArrayList<>();
        for (CertificateSetType.CertificateSetModel model : type.model) {
            MetadataVo metadata = getMetadata(model.getIdentifier());
            List<MetadataPropertyVo> propertyVoList = getMetadataProperty(metadata);
            CertificateSettingModelDto resultDto = createResultDto(locale, propertyVoList, model.getName(),
                    model.getIdentifier(),
                    metadata.getCustomProperties(), model.getRequired());
            result.add(resultDto);
        }
        return result;
    }

    private CertificateSettingModelDto createResultDto(Locale locale, List<MetadataPropertyVo> propertyList,
            String name, String IDENTIFIER, List<MetadataPropertyVo> customProperties, Set<String> required) {
        CertificateSettingModelDto resultDto = new CertificateSettingModelDto();
        List<SettingDto> dtos = BeanUtil.convertList(propertyList, SettingDto.class);
        // 这里控制显示那些参数 privateEmpProperty 显示参数的集合
        dtos = Sequences.sequence(dtos)
                .map(mpv -> {
                    if (required.contains(mpv.getProperty())) {
                        mpv.setRemake("true");
                    }
                    mpv.setEntity(name);
                    if (ObjectUtil.isNotEmpty(mpv.getI18nName())) {
                        mpv.setName(LangUtil.getCurrentLangVal(mpv.getI18nName(), locale));
                    }
                    mpv.setProperty(IDENTIFIER + "@" + mpv.getProperty());
                    return mpv;
                }).toList();
        dtos.addAll(Sequences.sequence(customProperties).map(p -> {
            SettingDto mpv = ObjectConverter.convert(p, SettingDto.class);
            mpv.setEntity(name);
            if (ObjectUtil.isNotEmpty(mpv.getI18nName())) {
                mpv.setName(LangUtil.getCurrentLangVal(mpv.getI18nName(), locale));
            }
            mpv.setProperty(IDENTIFIER + "@ext@" + mpv.getProperty());
            return mpv;
        }));
        resultDto.setName(name);
        resultDto.setIdentifier(IDENTIFIER);
        resultDto.setProperties(dtos);
        return resultDto;
    }

    public MetadataVo getMetadata(String identifier) {
        MetadataVo metadata = metadataOperatorService.load(identifier);
        return metadata;
    }

    public List<MetadataPropertyVo> getMetadataProperty(MetadataVo metadata) {
        List<MetadataPropertyVo> allProperties = Optional.ofNullable(metadata).map(MetadataVo::getStandardProperties)
                .orElse(Lists.newArrayList());
        allProperties.addAll(Optional.ofNullable(metadata).map(MetadataVo::getInheritedStandardProperties)
                .orElse(Lists.newArrayList()));
        // allProperties.addAll(Optional.ofNullable(metadata).map(MetadataVo::getCustomProperties).orElse(Lists.newArrayList()));
        return allProperties;
    }

    public Object dragSort(List<String> ids) {
        int sort = 0;
        for (String id : ids) {
            CertificateSettingDo settingDo = certificateSettingDo.selectById(id);
            settingDo.setCertificateBid(String.valueOf(sort++));
            certificateSettingDo.update(settingDo);
        }
        return true;
    }

    public List<SettingDto> selectList(CertificateSetType certificate) {
        CertificateSettingDo settingDo = new CertificateSettingDo();
        settingDo.setTypeCode(certificate.code);
        return selectSettingDtoList(settingDo);
    }
}
