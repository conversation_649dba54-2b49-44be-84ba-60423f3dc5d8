package com.caidaocloud.certificate.service.certificate.application.service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.handler.impl.ExcelDataHandlerDefaultImpl;
import com.caidaocloud.certificate.service.certificate.application.event.CertificateAcquiredEvent;
import com.caidaocloud.certificate.service.certificate.domain.entity.CeritificateAndEmpDo;
import com.caidaocloud.certificate.service.certificate.domain.entity.CertificateDo;
import com.caidaocloud.certificate.service.certificate.domain.entity.CertificateProjectDo;
import com.caidaocloud.certificate.service.certificate.domain.entity.CertificateUsageRecordDo;
import com.caidaocloud.certificate.service.certificate.domain.enums.CertificateUseRecordStatus;
import com.caidaocloud.certificate.service.certificate.domain.enums.ProjectStatus;
import com.caidaocloud.certificate.service.certificate.domain.factory.CertificateProjectFactory;
import com.caidaocloud.certificate.service.certificate.domain.repository.ICeritificateAndEmpRepository;
import com.caidaocloud.certificate.service.certificate.domain.repository.ICertificateRepository;
import com.caidaocloud.certificate.service.certificate.domain.repository.IEmpInfoRepository;
import com.caidaocloud.certificate.service.certificate.domain.repository.IWorkplaceRepository;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CertificateProjectDto;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CertificateProjectQueryDto;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CertificateUsageRecordQueryDto;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CertificateUsageRecordSaveDto;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CertificateUsageRecordUpdateDto;
import com.caidaocloud.certificate.service.certificate.interfaces.vo.CertificateProjectPageVo;
import com.caidaocloud.certificate.service.certificate.interfaces.vo.CertificateProjectVo;
import com.caidaocloud.certificate.service.certificate.interfaces.vo.CertificateUsageRecordDetailVo;
import com.caidaocloud.certificate.service.certificate.interfaces.vo.CertificateUsageRecordExportVo;
import com.caidaocloud.certificate.service.certificate.interfaces.vo.CertificateUsageRecordPageVo;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.hrpaas.metadata.sdk.transaction.annotation.PaasTransactional;
import com.caidaocloud.masterdata.entity.emp.EmpInfoEntity;
import com.caidaocloud.masterdata.entity.org.WorkPlaceEntity;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.BeanUtil;
import com.caidaocloud.util.DateUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Sequences;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class CertificateProjectService {

    @Autowired
    private CertificateProjectFactory certificateProjectFactory;
    @Autowired
    private IWorkplaceRepository workplaceRepository;
    @Autowired
    private IEmpInfoRepository empInfoRepository;
    @Autowired
    private ICertificateRepository certificateRepository;
    @Resource
    private ICeritificateAndEmpRepository ceritificateAndEmpRepository;

    // @PaasTransactional
    public String createCertificateProject(CertificateProjectDto dto) {
        CertificateProjectDo certificateProject = certificateProjectFactory.createProject(dto);
        certificateProject.save();
        return certificateProject.getBid();
    }

    // @PaasTransactional
    public void updateCertificateProject(CertificateProjectDto dto) {
        String bid = dto.getBid();
        CertificateProjectDo existingProject = findProjectById(bid);
        CertificateProjectDo data = certificateProjectFactory.convert2Entity(dto, existingProject);
        data.save();
    }

    private CertificateProjectDo findProjectById(String projectId) {
        return CertificateProjectDo.findById(projectId)
                .orElseThrow(() -> new ServerException("Certificate project not found with ID: " + projectId));
    }

    public CertificateProjectDto getCertificateProject(String projectId) {
        return ObjectConverter.convert(findProjectById(projectId), CertificateProjectDto.class);
    }

    public CertificateProjectVo getCertificateProjectVo(String projectId) {
        CertificateProjectDto project = getCertificateProject(projectId);
        CertificateProjectVo vo = ObjectConverter.convert(project, CertificateProjectVo.class);
        List<EmpInfoEntity> empInfoEntityList = empInfoRepository.queryListByEmpIds(Lists.list(project.getEmpId()));
        if (!empInfoEntityList.isEmpty()) {
            vo.setEmpId(FastjsonUtil.convertObject(empInfoEntityList.get(0), EmpSimple.class));
        }
        List<WorkPlaceEntity> workPlaceEntityList = workplaceRepository.loadList(Lists.list(project.getLocation()));
        if (!workPlaceEntityList.isEmpty()) {
            vo.setLocationTxt(workPlaceEntityList.get(0).getName());
        }
        return vo;
    }

    @PaasTransactional
    public void deleteCertificateProject(String projectId) {
        log.info("项目登记删除，projectId={}", projectId);
        findProjectById(projectId).delete();
        for (CertificateUsageRecordDo usageRecord : CertificateUsageRecordDo
                .page(new CertificateUsageRecordQueryDto(projectId, -1, 1))
                .getItems()) {
            usageRecord.delete();
            new CertificateAcquiredEvent(SecurityUserUtil.getSecurityUserInfo()
                    .getTenantId(), usageRecord.getEmpCertificate()).publish();
        }
    }

    public PageResult<CertificateProjectPageVo> getCertificateProjectPage(CertificateProjectQueryDto dto) {
        PageResult<CertificateProjectDo> pageResult = CertificateProjectDo.page(dto);
        if (pageResult.getItems().isEmpty()) {
            return new PageResult<>(new ArrayList<>(), pageResult.getPageNo(), pageResult.getPageSize(),
                    pageResult.getTotal());
        }
        List<String> empIdList = pageResult.getItems().stream().map(CertificateProjectDo::getEmpId)
                .collect(Collectors.toList());
        List<EmpInfoEntity> empInfoEntityList = empInfoRepository.queryListByEmpIds(empIdList);
        List<String> workplaceList = pageResult.getItems().stream().map(CertificateProjectDo::getLocation)
                .collect(Collectors.toList());
        List<WorkPlaceEntity> workPlaceEntityList = workplaceRepository.loadList(workplaceList);
        return new PageResult(pageResult.getItems().stream()
                .map(item -> {
                    CertificateProjectPageVo vo = ObjectConverter.convert(item, CertificateProjectPageVo.class);
                    vo.setEmp(Sequences.sequence(empInfoEntityList)
                            .find(e -> e.getEmpId().equals(item.getEmpId()))
                            .map(emp -> FastjsonUtil.convertObject(emp, EmpSimple.class))
                            .getOrNull());
                    vo.setLocationTxt(Sequences.sequence(workPlaceEntityList)
                            .find(w -> w.getBid().equals(item.getLocation())).map(WorkPlaceEntity::getName)
                            .getOrNull());
                    return vo;
                })
                .collect(Collectors.toList()), pageResult.getPageNo(), pageResult.getPageSize(), pageResult.getTotal());
    }

    // @PaasTransactional
    public void createCertificateUsageRecord(CertificateUsageRecordSaveDto dto) {
        for (String empCertificate : dto.getEmpCertificate()) {
            CertificateUsageRecordDo certificateProject = certificateProjectFactory.createUsageRecord(dto,
                    empCertificate);
            certificateProject.save();
            log.info("证书使用记录新增，record={}", certificateProject);
            new CertificateAcquiredEvent(SecurityUserUtil.getSecurityUserInfo()
                    .getTenantId(), empCertificate).publish();
        }
    }

    // @PaasTransactional
    public void updateCertificateUsageRecord(CertificateUsageRecordUpdateDto dto) {
        String bid = dto.getBid();
        CertificateUsageRecordDo existingProject = findUsageRecordById(bid);
        BeanUtil.copyWithNoValue(certificateProjectFactory.updateUsageRecord(dto), existingProject);
        existingProject.save();
        log.info("证书使用记录编辑，record={}", existingProject);
        new CertificateAcquiredEvent(SecurityUserUtil.getSecurityUserInfo()
                .getTenantId(), existingProject.getEmpCertificate()).publish();
    }

    private CertificateUsageRecordDo findUsageRecordById(String bid) {
        return CertificateUsageRecordDo.findById(bid)
                .orElseThrow(() -> new ServerException("Certificate usage record not found with ID: " + bid));
    }

    public CertificateUsageRecordDetailVo getCertificateUsageRecordDetail(String bid) {
        CertificateUsageRecordDo record = findUsageRecordById(bid);
        CertificateUsageRecordDetailVo vo = ObjectConverter.convert(record, CertificateUsageRecordDetailVo.class);
        List<EmpInfoEntity> entityList = empInfoRepository.queryListByEmpIds(Lists.list(record.getEmpId()));
        if (!entityList.isEmpty()) {
            vo.setEmp(FastjsonUtil.convertObject(entityList.get(0), EmpSimple.class));
        }
        vo.display();
        return vo;

    }

    // @PaasTransactional
    public void deleteCertificateUsageRecord(String bid) {
        CertificateUsageRecordDo data = findUsageRecordById(bid);
        data.delete();
        log.info("证书使用记录删除，record={}", data);
        new CertificateAcquiredEvent(SecurityUserUtil.getSecurityUserInfo()
                .getTenantId(), data.getEmpCertificate()).publish();
    }

    public PageResult<CertificateUsageRecordPageVo> getAllCertificateUsageRecordsByProject(
            CertificateUsageRecordQueryDto dto) {
        PageResult<CertificateUsageRecordDo> pageResult = CertificateUsageRecordDo.page(dto);
        if (pageResult.getItems().isEmpty()) {
            return new PageResult<>(new ArrayList<>(), pageResult.getPageNo(), pageResult.getPageSize(),
                    pageResult.getTotal());
        }
        List<CertificateDo> certificateDoList = certificateRepository.selectListByIds(pageResult.getItems().stream()
                .map(CertificateUsageRecordDo::getCertificate).collect(Collectors.toList()));
        List<String> empIdList = pageResult.getItems().stream().map(CertificateUsageRecordDo::getEmpId)
                .collect(Collectors.toList());
        List<EmpInfoEntity> empInfoEntityList = empInfoRepository.queryListByEmpIds(empIdList);
        List<CeritificateAndEmpDo> ceritificateAndEmpDoList = ceritificateAndEmpRepository
                .selectBatchIds(pageResult.getItems()
                        .stream()
                        .map(CertificateUsageRecordDo::getEmpCertificate)
                        .collect(Collectors.toList()));
        return new PageResult(pageResult.getItems().stream()
                .map(item -> {
                    CertificateUsageRecordPageVo vo = ObjectConverter.convert(item, CertificateUsageRecordPageVo.class);
                    vo.display();
                    vo.setEmp(Sequences.sequence(empInfoEntityList)
                            .find(e -> e.getEmpId().equals(item.getEmpId()))
                            .map(emp -> FastjsonUtil.convertObject(emp, EmpSimple.class))
                            .getOrNull());
                    vo.setCertificateTxt(Sequences.sequence(certificateDoList)
                            .find(w -> w.getBid().equals(item.getCertificate())).map(CertificateDo::getName)
                            .getOrNull());
                    vo.setCertificateActiveStatus(Sequences.sequence(ceritificateAndEmpDoList)
                            .find(w -> w.getBid().equals(item.getEmpCertificate())).map(data -> {
                                data.checkActive(System.currentTimeMillis());
                                return data.getStatus();
                            }).getOrElse(() -> {
                                EnumSimple enumSimple = new EnumSimple();
                                enumSimple.setText("无效");
                                enumSimple.setValue("1");
                                return enumSimple;
                            }));
                    return vo;
                })
                .collect(Collectors.toList()), pageResult.getPageNo(), pageResult.getPageSize(), pageResult.getTotal());
    }

    public Workbook exportAllCertificateUsageRecordsByProject(CertificateUsageRecordQueryDto dto) {
        dto.setPageSize(-1);
        List<CertificateUsageRecordPageVo> data = getAllCertificateUsageRecordsByProject(dto).getItems();
        List<CertificateUsageRecordExportVo> exportData = data.stream().map(d -> {
            CertificateUsageRecordExportVo vo = ObjectConverter.convert(d, CertificateUsageRecordExportVo.class);
            if (d.getEmp() != null) {
                vo.setName(d.getEmp().getName());
                vo.setWorkno(d.getEmp().getWorkno());
            }
            vo.setCertificateActiveStatus(d.getCertificateActiveStatus().getText());
            return vo;
        }).collect(Collectors.toList());
        ExportParams params = new ExportParams();
        params.setDataHandler(new ExcelDataHandlerDefaultImpl() {

            @Override
            public String[] getNeedHandlerFields() {
                return new String[] { "登记类别", "证书登记状态", "证书登记使用日期", "证书结束使用日期" };
            }

            @Override
            public Object exportHandler(Object obj, String name, Object value) {
                if (value == null) {
                    return null;
                }
                if ("登记类别".equals(name)) {
                    return ((DictSimple) value).getText();
                } else if ("证书登记状态".equals(name)) {
                    return ((CertificateUseRecordStatus) value).getDescription();
                } else {
                    return DateUtil.formatDate((Long) value);
                }
            }
        });
        return ExcelExportUtil.exportExcel(params, CertificateUsageRecordExportVo.class, exportData);
    }

    public Workbook exportCertificateProjectPage(CertificateProjectQueryDto dto) {
        dto.setPageSize(-1);
        List<CertificateProjectPageVo> data = getCertificateProjectPage(dto).getItems();
        ExportParams params = new ExportParams();
        params.setDataHandler(new ExcelDataHandlerDefaultImpl() {

            @Override
            public String[] getNeedHandlerFields() {
                return new String[] { "项目负责人", "开工日期", "竣工日期", "项目状态", "证书登记使用日期", "证书结束使用日期", "证书登记状态" };
            }

            @Override
            public Object exportHandler(Object obj, String name, Object value) {
                if (value == null) {
                    return null;
                }
                if ("项目负责人".equals(name)) {
                    return String.format("%s(%s)", ((EmpSimple) value).getWorkno(), ((EmpSimple) value).getName());
                } else if ("项目状态".equals(name)) {
                    return ((ProjectStatus) value).getDescription();
                } else if ("证书登记状态".equals(name)) {
                    return ((CertificateUseRecordStatus) value).getDescription();
                } else {
                    return DateUtil.formatDate((Long) value);
                }
            }
        });
        return ExcelExportUtil.exportExcel(params, CertificateProjectPageVo.class, data);

    }
}
