package com.caidaocloud.certificate.service.certificate.interfaces.dto;

import com.caidaocloud.certificate.service.certificate.domain.enums.CertificateUseRecordStatus;
import com.caidaocloud.certificate.service.certificate.domain.enums.ProjectStatus;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @date 2024/4/15
 */
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "证书项目数据传输对象Dto")
@Data
public class CertificateProjectDto {

	@ApiModelProperty(notes = "项目ID")
	private String bid;

	@ApiModelProperty(notes = "组织ID")
	private String organize;

	@ApiModelProperty(notes = "项目名称")
	private String projectName;

	@ApiModelProperty(notes = "项目负责人ID")
	private String empId;

	@ApiModelProperty(notes = "项目地点")
	private String location;

	@ApiModelProperty(notes = "项目开始日期（毫秒时间戳）")
	private Long startDate;

	@ApiModelProperty(notes = "项目结束日期（毫秒时间戳）")
	private Long endDate;

	@ApiModelProperty(notes = "项目状态")
	private ProjectStatus projectStatus;

	@ApiModelProperty(notes = "证书开始日期（毫秒时间戳）")
	private Long certificateStartDate;

	@ApiModelProperty(notes = "证书结束日期（毫秒时间戳）")
	private Long certificateEndDate;

	@ApiModelProperty(notes = "证书状态")
	private CertificateUseRecordStatus certificateStatus;
}

