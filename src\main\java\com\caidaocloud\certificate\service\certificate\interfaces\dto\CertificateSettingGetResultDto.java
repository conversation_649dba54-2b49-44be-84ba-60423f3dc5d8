package com.caidaocloud.certificate.service.certificate.interfaces.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * CreateName: Mr.Li
 * CreateTime: 2024/7/31
 * 证书设置获取结果DTO
 **/
@Data
@ApiModel(description = "证书设置获取结果DTO")
public class CertificateSettingGetResultDto {
    
    @ApiModelProperty("模型")
    private String model;
    
    @ApiModelProperty("属性列表")
    private List<SettingDto> properties;
}
