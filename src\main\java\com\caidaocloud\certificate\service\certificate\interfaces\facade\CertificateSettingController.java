package com.caidaocloud.certificate.service.certificate.interfaces.facade;

import com.caidaocloud.certificate.service.certificate.application.service.CertificateSettingService;
import com.caidaocloud.certificate.service.certificate.domain.entity.CertificateSettingDo;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CeritificateSettingDto;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CertificateSetSortDto;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * CreateName: Mr.Li
 * CreateTime: 2024/5/20 16:15
 **/
@RestController
@RequestMapping("/api/certificate/v1/set")
@Api(tags = "证书消息设置接口")
@Slf4j
public class CertificateSettingController {

    @Resource
    private CertificateSettingService certificateSettingService;
    @PostMapping("/list")
    @ApiOperation("列表/下拉列表")
    public Result selectList(@RequestBody CertificateSettingDo dto){
        return Result.ok(certificateSettingService.selectList(dto));
    }

    @PostMapping("/add")
    @ApiOperation("新增")
    public Result add(@RequestBody CertificateSettingDo dto){
        return Result.ok(certificateSettingService.add(dto));
    }

    @PostMapping("/del")
    @ApiOperation("删除")
    public Result del(@RequestBody CertificateSettingDo dto){
        certificateSettingService.del(dto);
        return Result.ok();
    }

    @PostMapping("/updata")
    @ApiOperation("更新")
    public Result updata(@RequestBody CertificateSettingDo dto){
        certificateSettingService.updata(dto);
        return Result.ok();
    }

    @PostMapping("/saveBath")
    @ApiOperation("保存")
    public Result saveBath(@RequestBody CeritificateSettingDto dto){
        certificateSettingService.saveBatch(dto);
        return Result.ok();
    }

    @PostMapping("/get")
    @ApiOperation("详情接口")
    public Result get(@RequestBody CertificateSettingDo dto){
        return Result.ok(certificateSettingService.get(dto));
    }

    @PostMapping("/set")
    @ApiOperation("选择可选字段")
    public Result set(@RequestBody CeritificateSettingDto dto){
        return Result.ok(certificateSettingService.set(dto.getTypeCode()));
    }

    @PostMapping("/dragSort")
    @ApiOperation("排序")
    public Result dragSort(@RequestBody List<String> ids){
        return Result.ok(certificateSettingService.dragSort(ids));
    }


}
