package com.caidaocloud.certificate.service.certificate.interfaces.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.caidaocloud.certificate.service.certificate.domain.enums.CertificateUseRecordStatus;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.util.DateUtil;
import groovy.util.logging.Slf4j;
import lombok.Data;

/**
 * CreateName: Mr.Li
 * CreateTime: 2024/4/9 17:33
 * 证书类型查询dto
 **/
@Data
@Slf4j
public class CertificateUsageRecordExportVo {
    @Excel(name = "登记类别")
    private DictSimple type;
    @Excel(name = "证书名称")
    private String certificateTxt;
    @Excel(name = "工号")
    private String workno;
    @Excel(name = "员工姓名")
    private String name;
    @Excel(name = "证书登记使用日期",width = 20)
    private Long startDate;
    @Excel(name = "证书结束使用日期",width = 20)
    private Long endDate;
    @Excel(name = "证书登记状态")
    private CertificateUseRecordStatus certificateStatus;
    @Excel(name = "证书状态")
    private String certificateActiveStatus;


    public void display(){
        if (startDate==0) {
            startDate = null;
        }
        if (endDate== DateUtil.MAX_TIMESTAMP) {
            endDate = null;
        }
    }
}
