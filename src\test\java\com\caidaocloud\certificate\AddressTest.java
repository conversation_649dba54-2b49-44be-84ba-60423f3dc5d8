package com.caidaocloud.certificate;

import com.caidaocloud.hrpaas.metadata.sdk.dto.Address;
import com.caidaocloud.util.FastjsonUtil;
import org.junit.Assert;
import org.junit.Test;

/**
 *
 * <AUTHOR>
 * @date 2024/5/22
 */
public class AddressTest {
	@Test
	public void address(){
		String json = "[\n"
				+ "  \"340000\",\n"
				+ "  \"340100\",\n"
				+ "  \"340102\"\n"
				+ "]";
		Address address = FastjsonUtil.toObject(json, Address.class);
		Assert.assertEquals("340000/340100/340102/null",address.doText());
	}
}
