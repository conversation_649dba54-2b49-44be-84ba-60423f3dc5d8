package com.caidaocloud.certificate.service.certificate.domain.repository;

import java.util.List;

import com.caidaocloud.certificate.service.certificate.domain.base.repository.BaseRepository;
import com.caidaocloud.certificate.service.certificate.domain.entity.CertificateUsageRecordDo;
import com.caidaocloud.certificate.service.certificate.domain.enums.CertificateUseRecordStatus;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CertificateUsageRecordQueryDto;
import com.caidaocloud.dto.PageResult;

import org.springframework.stereotype.Repository;

@Repository
public interface ICertificateUsageRecordRepository extends BaseRepository<CertificateUsageRecordDo> {
	CertificateUsageRecordDo save(CertificateUsageRecordDo record);

	PageResult<CertificateUsageRecordDo> selectPage(CertificateUsageRecordQueryDto dto);

	PageResult<CertificateUsageRecordDo> selectUsageRecordByCertificate(String bid, String empCertificate, CertificateUseRecordStatus status);

	List<CertificateUsageRecordDo> selectUsageRecordByTime(long currentTimestamp);

	PageResult<CertificateUsageRecordDo> selectUsageRecordPageByTime(long currentTimestamp, int pageSize, int pageNo);

	List<CertificateUsageRecordDo> listByRange(String ceritifiCateBid, Long startTime, Long endTime, String empCertificate);
}
