package com.caidaocloud.certificate.service.certificate.interfaces.vo;

import com.caidaocloud.certificate.service.certificate.domain.enums.CertificateUseRecordStatus;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.util.DateUtil;
import groovy.util.logging.Slf4j;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * CreateName: Mr.Li
 * CreateTime: 2024/4/9 17:33
 * 证书类型查询dto
 **/
@Data
@Slf4j
@ApiModel(description = "证书使用情况详情Vo")
public class CertificateUsageRecordDetailVo {
    private String bid;
    @ApiModelProperty( "证书id")
    private String certificate;
    @ApiModelProperty( "登记类别")
    private DictSimple type;
    private Long startDate;
    private Long endDate;
    @ApiModelProperty("员工")
    private EmpSimple emp;
    @ApiModelProperty("证书状态")
    private CertificateUseRecordStatus certificateStatus;
    private String remark;


    public void display(){
        if (startDate==0) {
            startDate = null;
        }
        if (endDate== DateUtil.MAX_TIMESTAMP) {
            endDate = null;
        }
    }
}
