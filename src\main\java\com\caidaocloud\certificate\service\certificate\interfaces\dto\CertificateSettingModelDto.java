package com.caidaocloud.certificate.service.certificate.interfaces.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * CreateName: Mr.Li
 * CreateTime: 2024/7/31
 * 证书设置结果DTO
 **/
@Data
@ApiModel(description = "证书设置结果DTO")
public class CertificateSettingModelDto {
    
    @ApiModelProperty("名称")
    private String name;
    
    @ApiModelProperty("标识符")
    private String identifier;
    
    @ApiModelProperty("属性列表")
    private List<SettingDto> properties;
}
