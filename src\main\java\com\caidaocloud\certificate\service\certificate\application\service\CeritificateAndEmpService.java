package com.caidaocloud.certificate.service.certificate.application.service;

import com.caidaocloud.certificate.service.certificate.application.dto.CertificateCardDto;
import com.caidaocloud.certificate.service.certificate.application.dto.EmpInfoDto;
import com.caidaocloud.certificate.service.certificate.application.dto.EmpSearchDto;
import com.caidaocloud.certificate.service.certificate.application.feign.IHrWorkFeign;
import com.caidaocloud.certificate.service.certificate.application.feign.MasterDataEmpInfoFeign;
import com.caidaocloud.certificate.service.certificate.domain.base.util.LangUtil;
import com.caidaocloud.certificate.service.certificate.domain.base.util.ObjectConvertUtil;
import com.caidaocloud.certificate.service.certificate.domain.base.util.TagProperty;
import com.caidaocloud.certificate.service.certificate.domain.entity.*;
import com.caidaocloud.certificate.service.certificate.domain.enums.BooleanValueEnum;
import com.caidaocloud.certificate.service.certificate.domain.enums.CertificateStatusEnum;
import com.caidaocloud.certificate.service.certificate.domain.enums.CertificateUseStatusEnum;
import com.caidaocloud.certificate.service.certificate.domain.enums.ValidValueEnum;
import com.caidaocloud.certificate.service.certificate.domain.repository.ICeritificateAndEmpRepository;
import com.caidaocloud.certificate.service.certificate.domain.repository.IEmpInfoRepository;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.*;
import com.caidaocloud.certificate.service.certificate.interfaces.vo.*;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.dto.AbstractData;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.PropertyValue;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.util.DateUtil;
import com.caidaocloud.hrpaas.paas.common.dto.FormDataDto;
import com.caidaocloud.hrpaas.paas.common.dto.FormDefDto;
import com.caidaocloud.hrpaas.paas.common.dto.FormDefMetadataDto;
import com.caidaocloud.hrpaas.paas.common.feign.FormFeignClient;
import com.caidaocloud.masterdata.dto.EmpWorkInfoVo;
import com.caidaocloud.masterdata.entity.emp.EmpInfoEntity;
import com.caidaocloud.util.*;
import com.caidaocloud.web.Result;
import com.caidaocloud.workflow.configuration.WfFunctionConfiguration;
import com.caidaocloud.workflow.dto.WfMetaCallbackDto;
import com.caidaocloud.workflow.dto.WfMetaFunDto;
import com.caidaocloud.workflow.dto.WfMetaFunFormFieldDto;
import com.caidaocloud.workflow.enums.WfCallbackTimeTypeEnum;
import com.caidaocloud.workflow.enums.WfCallbackTypeEnum;
import com.caidaocloud.workflow.enums.WfFieldDataTypeEnum;
import com.caidaocloud.workflow.enums.WfFunctionPageJumpType;
import com.caidaocloud.workflow.feign.IWfRegisterFeign;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import com.googlecode.totallylazy.Group;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Option;
import com.googlecode.totallylazy.Sequences;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.time.*;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * CreateName: Mr.Li
 * CreateTime: 2024/4/15 11:38
 **/
@Service
@Slf4j
public class CeritificateAndEmpService {
    @Resource
    private CertificateDo certificateDo;
    @Resource
    private IHrWorkFeign iHrWorkFeign;
    @Resource
    private ICeritificateAndEmpRepository ceritificateAndEmpRepository;
    @Resource
    private CeritificateAndEmpDo ceritificateAndEmpDo;
    @Resource
    private FormFeignClient formFeignClient;
    @Autowired
    private CertificateProjectService certificateProjectService;
    @Autowired
    private CertificateSettingService certificateSettingService;
    @Resource
    private IWfRegisterFeign iWfRegisterFeign;
    @Autowired
    private CertificateTypeDo certificateTypeDo;

    /**
     * 证书人员管理列表查询 证书概览
     */
    public PageResult<CeritificateAndEmpVo> selectList(CeritificateAndEmpQueryDto vo) {
        vo.setApiType("2");
        PageResult<CertificateCardDto> page = ceritificateAndEmpRepository.queryCardPage(vo, vo.getPageSize(),
                vo.getPageNo(), false);
        List<CertificateCardDto> items = page.getItems();
        PageResult pageResult = new PageResult(convertCardVo(items), page.getPageNo(), page.getPageSize(),
                page.getTotal());
        return pageResult;
    }

    private List<CeritificateAndEmpVo> convertCardVo(List<CertificateCardDto> items) {
        if (items.isEmpty()) {
            return new ArrayList<>();
        }
        long time = System.currentTimeMillis();
        List<CertificateDo> certificateDos = certificateDo.all(new CertificateQueryDto());
        Map<String, String> certificateI18nMap = certificateDos.stream()
                .collect(Collectors.toMap(AbstractData::getBid, CertificateDo::getI18nName));

        List<CertificateTypeDo> certificateTypeDos = certificateTypeDo.all(new CertificateTypeQueryDto());
        Map<String, CertificateTypeDo> typeI18nMap = certificateTypeDos.stream()
                .collect(Collectors.toMap(AbstractData::getBid, obj -> obj, (a, b) -> a));

        EmpSearchDto dto = new EmpSearchDto();
        dto.setEmpIds(Sequences.sequence(items).map(CertificateCardDto::getEmpId).toList());
        List<EmpInfoDto> empInfoDtoList = SpringUtil.getBean(MasterDataEmpInfoFeign.class).loadEmpInfoList(dto)
                .getData();
        Map<String, EmpInfoDto> empInfoDtoMap = empInfoDtoList.stream()
                .collect(Collectors.toMap(EmpInfoDto::getEmpId, obj -> obj, (a, b) -> a));

        // 根据证书排序
        List<String> sortedOneLevelCertificateTypeBids = certificateTypeDos.stream().filter(v -> v.getLevel().equals(1))
                .map(AbstractData::getBid).collect(Collectors.toList());
        List<String> sortedTwoLevelCertificateTypeBids = certificateTypeDos.stream().filter(v -> v.getLevel().equals(2))
                .map(AbstractData::getBid).collect(Collectors.toList());
        List<String> sortedCertificateBids = certificateDos.stream().map(AbstractData::getBid)
                .collect(Collectors.toList());
        for (CertificateCardDto certificateCardDto : items) {
            certificateCardDto.getCeritificateList().sort(Comparator
                    .comparingInt((CeritificateAndEmpDo vo) -> {
                        int idx = sortedOneLevelCertificateTypeBids.indexOf(vo.getTypeBid());
                        return idx == -1 ? Integer.MAX_VALUE : idx;
                    })
                    .thenComparingInt(vo -> {
                        int idx = sortedTwoLevelCertificateTypeBids.indexOf(vo.getTypeSubBid());
                        return idx == -1 ? Integer.MAX_VALUE : idx;
                    })
                    .thenComparingInt(vo -> {
                        int idx = sortedCertificateBids.indexOf(vo.getCeritifiCateBid());
                        return idx == -1 ? Integer.MAX_VALUE : idx;
                    }));
        }

        List<DataSimple> list = DataQuery.identifier("entity.hr.EmpWorkInfo")
                .limit(-1, 1)
                .filter(DataFilter.in("empId", Sequences.sequence(items).map(CertificateCardDto::getEmpId).toList())
                        .andNe("deleted", Boolean.TRUE.toString()), DataSimple.class)
                .getItems();
        Map<String, String> shebaodiMap = list.stream()
                .filter(data -> {
                    PropertyValue value = data.getProperties().get("shebaodi");
                    return value != null && value.toText() != null;
                })
                .collect(Collectors.toMap(data -> data.getProperties().get("empId").toText(),
                        data -> data.getProperties().get("shebaodi").toText()));

        return Sequences.sequence(items).map(data -> {
            EmpInfoDto empInfo = empInfoDtoMap.get(data.getEmpId());
            CeritificateAndEmpVo vo = ObjectConverter.convert(empInfo, CeritificateAndEmpVo.class);
            vo.setSocialSecurityTxt(shebaodiMap.get(data.getEmpId()));
            vo.setCertificateVo(new ArrayList<>());
            vo.setCertificateNums(0);
            vo.setJobLevel(empInfo.getJobGrade().getStartGradeName());
            vo.setOrganize(empInfo.getOrganizeTxt());
            vo.setPost(empInfo.getPostTxt());

            List<Group<String, CeritificateAndEmpDo>> groupList = Sequences.sequence(data.getCeritificateList())
                    .groupBy(CeritificateAndEmpDo::getTypeSubBid).toList();
            for (Group<String, CeritificateAndEmpDo> group : groupList) {
                String subTypeId = group.key();
                CertificateTypeDo typeDo = typeI18nMap.get(subTypeId);
                CertificateForEmpVo typeVo = new CertificateForEmpVo();
                typeVo.setSubTypeName(
                        LangUtil.getCurrentLangVal(FastjsonUtil.toObject(typeDo.getI18nName(), Map.class)));
                typeVo.setSubTypeCode(typeDo.getCode());

                typeVo.setDataVos(new ArrayList<>());
                for (CeritificateAndEmpDo certificate : group) {
                    if (!certificate.checkActive(time)) {
                        continue;
                    }
                    CertificateDataEmpVo cerVo = new CertificateDataEmpVo();
                    cerVo.setCertificateCode(certificate.getCeritifiCateCode());
                    String i18nMap = certificateI18nMap.get(certificate.getCeritifiCateBid());
                    cerVo.setCertificateName(i18nMap == null ? certificate.getCeritifiCateName()
                            : LangUtil.getCurrentLangVal(FastjsonUtil.toObject(i18nMap, Map.class)));
                    typeVo.getDataVos().add(cerVo);
                }
                if (typeVo.getDataVos().size() > 0) {
                    vo.setCertificateNums(vo.getCertificateNums() + typeVo.getDataVos().size());
                    vo.getCertificateVo().add(typeVo);
                }
            }
            return vo;
        }).toList();
    }

    @Async("taskExecutor")
    public EmpWorkInfoVo getEntity(String empId) {
        EmpWorkInfoVo data = SpringUtil.getBean(MasterDataEmpInfoFeign.class)
                .loadEmpWorkInfo(empId, System.currentTimeMillis()).getData();
        EmpSearchDto dto = new EmpSearchDto();
        dto.setEmpIds(Arrays.asList(empId));
        EmpInfoDto vo = SpringUtil.getBean(MasterDataEmpInfoFeign.class).loadEmpInfoList(dto).getData().get(0);
        data.setJobTxt(vo.getJobTxt());
        com.caidaocloud.hr.service.vo.EmpWorkInfoVo infoVo = iHrWorkFeign
                .getEmpWorkInfo(empId, System.currentTimeMillis()).getData();
        ContractVo contractVo = iHrWorkFeign.getEmpCurrentContract(empId).getData();
        data.setContractType(contractVo.getContractSettingType());
        data.setSocialSecurity(infoVo.getSocialSecurity());
        return data;
    }

    private void putpropertiesForEmp(CeritificateAndEmpVo empVo, EmpWorkInfoVo empInfoEntity) {

        empVo.setEmpId(empInfoEntity.getEmpId());
        empVo.setName(empInfoEntity.getName());
        empVo.setWorkno(empInfoEntity.getWorkno());
        empVo.setJob(empInfoEntity.getJobTxt());
        empVo.setHireDate(empInfoEntity.getHireDate());
        empVo.setJobLevel(empInfoEntity.getJobGrade().getStartGradeName() == null ? ""
                : empInfoEntity.getJobGrade().getStartGradeName());
        List<String> list = new ArrayList<>();
        list.add(empInfoEntity.getSocialSecurity().getProvince() == null ? "-1"
                : empInfoEntity.getSocialSecurity().getProvince().toString());
        list.add(empInfoEntity.getSocialSecurity().getCity() == null ? "-1"
                : empInfoEntity.getSocialSecurity().getCity().toString());
        list.add(empInfoEntity.getSocialSecurity().getArea() == null ? "-1"
                : empInfoEntity.getSocialSecurity().getArea().toString());
        empVo.setSocialSecurity(list);
        empVo.setSocialSecurityTxt("");
        if (!list.contains("-1")) {
            empVo.setSocialSecurityTxt(empInfoEntity.getSocialSecurity().getText());
        }
        empVo.setPostId(empInfoEntity.getPost());
        empVo.setJobId(empInfoEntity.getJob());
        empVo.setJobLevelId(empInfoEntity.getJobGrade().getStartLevel());
        empVo.setOrganizeId(empInfoEntity.getOrganize());

        empVo.setPost(empInfoEntity.getPostTxt());
        empVo.setPhoto(empInfoEntity.getPhoto());
        empVo.setOrganize(empInfoEntity.getOrganizeTxt());
    }

    /**
     * 计算工龄
     * 1。1 取 1
     */
    private Integer countAge(Long hireDateTime) {
        LocalDate currentDate = LocalDate.now();
        Instant instant = Instant.ofEpochMilli(hireDateTime);
        ZonedDateTime zonedDateTime = instant.atZone(ZoneId.systemDefault());
        LocalDate localDate = zonedDateTime.toLocalDate();
        Period period = Period.between(localDate, currentDate);
        return period.getYears();
    }

    /**
     * 证书明细
     *
     * @return
     */
    @Deprecated
    public PageResult listData(CeritificateAndEmpQueryDto vo) {
        HttpServletRequest request = WebUtil.getRequest();
        String header = request.getHeader("Accept-Language");

        if (StringUtils.isNotEmpty(vo.getApiType())) {
            if (vo.getApiType().equals("2")) {
                vo.setUseStatus("0");
                vo.setCeritificateStatus("0");
                vo.setEmpStatus("0");
            }
        }
        if (StringUtils.isEmpty(vo.getEmpStatus())) {
            vo.setEmpStatus("0");
        }
        PageResult<CeritificateAndEmpDo> page = ceritificateAndEmpRepository.queryTablePage(vo, vo.getPageSize(),
                vo.getPageNo());
        Sequences.sequence(page.getItems()).forEach(item -> item.checkStatusAndUseStatus());
        List<CeritificateAndEmpDo> doList = page.getItems();
        List<CeritificateAndEmpDto> list = ObjectConvertUtil.convertList(doList, CeritificateAndEmpDto.class,
                (t1, v1) -> {
                    v1.setI18nName(FastjsonUtil.toObject(t1.getI18nName(), Map.class));
                });
        Map<String, Integer> CertificateNum = countCertificatNums(list);
        if (CollectionUtils.isEmpty(list)) {
            return new PageResult();
        }
        // 最终结果集
        List<CeritificateAndEmpDto> result = new ArrayList<>();
        Map<String, EmpInfoDto> entityBatch = getEntityBatch(list);
        for (CeritificateAndEmpDto t : list) {
            String key = t.getEmpId() + "-" + t.getCeritifiCateBid();
            if (StringUtils.isNotEmpty(t.getEmpId())) {
                final EmpInfoDto empInfo = entityBatch.get(t.getEmpId());
                t.setPost(empInfo.getPostTxt());
                t.setPostId(empInfo.getPost());
                t.setOrganize(empInfo.getOrganizeTxt());
                t.setOrganizeId(empInfo.getOrganize());
                t.setJobLevelId(empInfo.getJobGrade().getStartLevel());
                t.setJobLevel(empInfo.getJobGrade().getStartGradeName());
                t.setEmpStatus(empInfo.getEmpStatus());
                t.setJob(empInfo.getJobTxt());
                t.setWorkName(empInfo.getName());
                t.setWorkNo(empInfo.getWorkno());
                t.setEmpId(empInfo.getEmpId());
                t.setEmpStatus(empInfo.getEmpStatus());
                // t.setWorkAge(Double.valueOf(countAge(empInfo.getHireDate())));
                List<String> social = new ArrayList<>();
                social.add(empInfo.getSocialSecurity().getProvince() == null ? "-1"
                        : empInfo.getSocialSecurity().getProvince().toString());
                social.add(empInfo.getSocialSecurity().getCity() == null ? "-1"
                        : empInfo.getSocialSecurity().getCity().toString());
                social.add(empInfo.getSocialSecurity().getArea() == null ? "-1"
                        : empInfo.getSocialSecurity().getArea().toString());
                t.setSocialSecurity(social);
            }
            result.add(t);
            CertificateNum.put(key, 1);
        }
        // 持证数量赋值
        result.forEach(t -> {

            t.setCertificateNums(CertificateNum.get(t.getEmpId()));
            CertificateDo certificate = certificateDo.selectById(t.getCeritifiCateBid());
            CertificateTypeDo typeDo = SpringUtil.getBean(CertificateTypeDo.class).selectById(t.getTypeBid());
            CertificateTypeDo subTypeDo = SpringUtil.getBean(CertificateTypeDo.class).selectById(t.getTypeSubBid());
            if (certificate != null && certificate.getI18nName() != null) {
                Map certificateI18n = FastjsonUtil.toObject(certificate.getI18nName(), Map.class);
                t.setCeritifiCateName(certificateI18n.get(header) == null ? (String) certificateI18n.get("default")
                        : (String) certificateI18n.get(header));
            }
            if (typeDo != null && typeDo.getI18nName() != null) {
                Map typeI18n = FastjsonUtil.toObject(typeDo.getI18nName(), Map.class);
                t.setTypeName(typeI18n.get(header) == null ? (String) typeI18n.get("default")
                        : (String) typeI18n.get(header));
            }
            if (subTypeDo != null && subTypeDo.getI18nName() != null) {
                Map subTypeI18n = FastjsonUtil.toObject(subTypeDo.getI18nName(), Map.class);
                t.setTypeSubName(subTypeI18n.get(header) == null ? (String) subTypeI18n.get("default")
                        : (String) subTypeI18n.get(header));
            }

            // 枚举
            EnumSimple isRegister = t.getIsRegister();
            isRegister.setText(BooleanValueEnum.getLanguageTxt(t.getIsRegister().getText(), header));
            t.setIsRegister(isRegister);
            EnumSimple isuse = t.getIsuse();
            isuse.setText(BooleanValueEnum.getLanguageTxt(t.getIsuse().getText(), header));
            t.setIsuse(isuse);

            // 枚举
            EnumSimple status = t.getStatus();
            status.setText(ValidValueEnum.getLanguageTxt(t.getStatus().getText(), header));
            t.setStatus(status);
            // 枚举
            EnumSimple useStatus = t.getUseStatus();
            useStatus.setText(ValidValueEnum.getUseLanguageTxt(t.getUseStatus().getText(), header));
            t.setUseStatus(useStatus);
        });
        result = result.stream().sorted(Comparator.comparing(t -> t.getWorkNo()))
                .sorted(Comparator.comparing(CeritificateAndEmpDto::getCreateTime).reversed())
                .collect(Collectors.toList());
        PageResult pageResult = new PageResult(result, vo.getPageNo(), vo.getPageSize(), page.getTotal());
        return pageResult;
    }

    private List<CeritificateAndEmpDto> convertTableVo(List<CeritificateAndEmpDo> items, String header) {
        if (items.isEmpty()) {
            return new ArrayList<>();
        }
        List<CertificateDo> certificateDos = certificateDo.selectList(new CertificateQueryDto());
        Map<String, CertificateDo> certificateI18nMap = certificateDos.stream()
                .collect(Collectors.toMap(AbstractData::getBid, obj -> obj, (a, b) -> a));

        List<CertificateTypeDo> certificateTypeDos = certificateTypeDo.selectList(new CertificateTypeQueryDto());
        Map<String, CertificateTypeDo> typeI18nMap = certificateTypeDos.stream()
                .collect(Collectors.toMap(AbstractData::getBid, obj -> obj, (a, b) -> a));

        EmpSearchDto dto = new EmpSearchDto();
        dto.setEmpIds(Sequences.sequence(items).map(CeritificateAndEmpDo::getEmpId).toList());
        List<EmpInfoDto> empInfoDtoList = SpringUtil.getBean(MasterDataEmpInfoFeign.class).loadEmpInfoList(dto)
                .getData();
        Map<String, EmpInfoDto> empInfoDtoMap = empInfoDtoList.stream()
                .collect(Collectors.toMap(EmpInfoDto::getEmpId, obj -> obj, (a, b) -> {
                    log.warn("Found duplicate data,empId={}", a.getEmpId());
                    return a;
                }));
        return Sequences.sequence(items).map(data -> {
            data.checkStatusAndUseStatus();
            CeritificateAndEmpDto vo = ObjectConverter.convert(data, CeritificateAndEmpDto.class);
            EmpInfoDto empInfo = empInfoDtoMap.get(data.getEmpId());
            vo.setEmpId(empInfo.getEmpId());
            vo.setWorkName(empInfo.getName());
            vo.setWorkNo(empInfo.getWorkno());
            vo.setOrganize(empInfo.getOrganizeTxt());
            vo.setPost(empInfo.getPostTxt());

            CertificateDo certificate = certificateI18nMap.get(vo.getCeritifiCateBid());
            CertificateTypeDo typeDo = typeI18nMap.get(vo.getTypeBid());
            CertificateTypeDo subTypeDo = typeI18nMap.get(vo.getTypeSubBid());
            if (certificate != null && certificate.getI18nName() != null) {
                Map certificateI18n = FastjsonUtil.toObject(certificate.getI18nName(), Map.class);
                vo.setCeritifiCateName(certificateI18n.get(header) == null ? (String) certificateI18n.get("default")
                        : (String) certificateI18n.get(header));
            }
            if (typeDo != null && typeDo.getI18nName() != null) {
                Map typeI18n = FastjsonUtil.toObject(typeDo.getI18nName(), Map.class);
                vo.setTypeName(typeI18n.get(header) == null ? (String) typeI18n.get("default")
                        : (String) typeI18n.get(header));
            }
            if (subTypeDo != null && subTypeDo.getI18nName() != null) {
                Map subTypeI18n = FastjsonUtil.toObject(subTypeDo.getI18nName(), Map.class);
                vo.setTypeSubName(subTypeI18n.get(header) == null ? (String) subTypeI18n.get("default")
                        : (String) subTypeI18n.get(header));
            }
            if (data.getI18nName() != null)
                vo.setSpecialty(LangUtil.getCurrentLangVal(FastjsonUtil.toObject(data.getI18nName(), Map.class)));

            vo.getIsRegister().setText(BooleanValueEnum.getLanguageTxt(vo.getIsRegister().getText(), header));
            vo.getIsuse().setText(BooleanValueEnum.getLanguageTxt(vo.getIsuse().getText(), header));
            vo.getStatus().setText(ValidValueEnum.getLanguageTxt(vo.getStatus().getText(), header));
            vo.getUseStatus().setText(ValidValueEnum.getUseLanguageTxt(vo.getUseStatus().getText(), header));
            return vo;
        }).toList();
    }

    private Map<String, Integer> countCertificatNums(List<CeritificateAndEmpDto> list) {
        Map<String, List<CeritificateAndEmpDto>> collect = list.stream()
                .collect(Collectors.groupingBy(CeritificateAndEmpDto::getEmpId));
        return collect.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, e -> e.getValue().size()));
    }

    /**
     * 批量处理员工信息
     *
     * @param list
     */
    public Map<String, EmpInfoDto> getEntityBatch(List<CeritificateAndEmpDto> list) {
        List<String> empIds = list.stream().map(CeritificateAndEmpDto::getEmpId).collect(Collectors.toList());
        EmpSearchDto dto = new EmpSearchDto();
        dto.setEmpIds(empIds);
        dto.setDatetime(System.currentTimeMillis());
        List<EmpInfoDto> data = SpringUtil.getBean(MasterDataEmpInfoFeign.class).loadEmpInfoList(dto).getData();
        data.forEach(t -> {
            // 脏数据处理
            if (t.getEmpStatus().getValue() == null) {
                EnumSimple enumSimple = new EnumSimple();
                enumSimple.setValue("0");
                t.setEmpStatus(enumSimple);
            }
        });
        Map<String, EmpInfoDto> empInfoDtoMap = data.stream()
                .collect(Collectors.toMap(EmpInfoDto::getEmpId, t -> t, (v1, v2) -> v1));
        return empInfoDtoMap;
    }

    /**
     * 赋值证书相关属性
     *
     * @param result
     */
    // @Async("taskExecutor")
    public void putpropertiesForCertificate(List<CertificateCardDto> listCar, List<CeritificateAndEmpVo> result) {
        List<String> list = Lists.list();
        List<CertificateDo> certificateDos = certificateDo.selectList(new CertificateQueryDto());
        Map<String, String> certificateI18nMap = certificateDos.stream()
                .collect(Collectors.toMap(AbstractData::getBid, CertificateDo::getI18nName));
        if (ObjectUtil.isNotEmpty(listCar)) {
            listCar.forEach(t -> {
                CeritificateAndEmpVo empVo = new CeritificateAndEmpVo();
                List<CertificateForEmpVo> vos = Lists.list();

                if (!list.contains(t.getEmpId())) {
                    list.add(t.getEmpId());
                    EmpWorkInfoVo empInfoEntity = getEntity(t.getEmpId());
                    // 脏数据处理
                    if (empInfoEntity.getEmpStatus().getValue() == null) {
                        EnumSimple enumSimple = new EnumSimple();
                        enumSimple.setValue("0");
                        empInfoEntity.setEmpStatus(enumSimple);
                    }
                    // 复制员工数据
                    empVo.setEmpStatus(empInfoEntity.getEmpStatus());
                    putpropertiesForEmp(empVo, empInfoEntity);
                    empVo.setWorkAge(countAge(empVo.getHireDate()));

                }
                List<CeritificateAndEmpDo> ceritificateList = t.getCeritificateList();
                Map<String, List<CeritificateAndEmpDo>> collect = ceritificateList.stream()
                        .collect(Collectors.groupingBy(CeritificateAndEmpDo::getTypeSubBid));
                for (Map.Entry<String, List<CeritificateAndEmpDo>> stringListEntry : collect.entrySet()) {
                    List<CeritificateAndEmpDo> ceritificateAndEmpDo = stringListEntry.getValue();
                    int certificateNum = ceritificateAndEmpDo.size();
                    CertificateTypeDo subTypeDo = certificateTypeDo.selectById(stringListEntry.getKey());
                    if (subTypeDo == null || (subTypeDo.getName() == null) || (subTypeDo.getCode() == null)) {
                        log.error("该证书类型已经不存在subTypeBid： " + stringListEntry.getKey());
                        continue;
                    }
                    CertificateForEmpVo vo = new CertificateForEmpVo();
                    vo.setSubTypeName(
                            LangUtil.getCurrentLangVal(FastjsonUtil.toObject(subTypeDo.getI18nName(), Map.class)));
                    vo.setSubTypeCode(subTypeDo.getCode());

                    List<CertificateDataEmpVo> dataVos = new ArrayList<>();
                    for (CeritificateAndEmpDo t1 : ceritificateAndEmpDo) {
                        if (t1.isDeleted()) {
                            continue;
                        }
                        if (t1.getExpireTime() == null) {

                        } else {
                            Date date = new Date(Long.parseLong(t1.getExpireTime()));
                            Date nowData = new Date(System.currentTimeMillis());
                            if (date.before(nowData)) {
                                continue;
                            }
                        }

                        CertificateDataEmpVo cerVo = new CertificateDataEmpVo();

                        cerVo.setCertificateCode(t1.getCeritifiCateCode());
                        String i18nMap = certificateI18nMap.get(t1.getCeritifiCateBid());
                        cerVo.setCertificateName(i18nMap == null ? t1.getCeritifiCateName()
                                : LangUtil.getCurrentLangVal(FastjsonUtil.toObject(i18nMap, Map.class)));

                        dataVos.add(cerVo);

                    }
                    vo.setDataVos(dataVos);
                    vos.add(vo);
                    certificateNum += empVo.getCertificateNums() == null ? 0 : empVo.getCertificateNums();
                    empVo.setCertificateNums(certificateNum);
                }

                empVo.setCertificateVo(vos);
                result.add(empVo);

            });
        } else {
            CeritificateAndEmpVo empVo = new CeritificateAndEmpVo();
            CertificateForEmpVo vo = new CertificateForEmpVo();
            empVo.setCertificateVo(Arrays.asList(vo));
        }

    }

    /**
     * 新增编辑
     */
    public String saveOrUpdate(CeritificateAndEmpDto vo) {
        // 多语言
        CeritificateAndEmpDo doData = ObjectConverter.convert(vo, CeritificateAndEmpDo.class);
        doData.setI18nName(LangUtil.getI18nValue(vo.getSpecialty(), vo.getI18nName()));

        CertificateDo certificate = SpringUtil.getBean(CertificateDo.class).selectById(vo.getCeritifiCateBid());
        PreCheck.preCheckArgument(ObjectUtil.isEmpty(certificate), "证书不存在");

        EmpWorkInfoVo empInfoEntity = getEntity(vo.getEmpId());
        PreCheck.preCheckArgument(StringUtils.isEmpty(empInfoEntity.getBid()), "员工不存在");
        PreCheck.preCheckArgument(ObjectUtil.isEmpty(empInfoEntity.getOrganize()), "组织不存在");
        // 冗余字段 用于权限
        doData.setOrganizeId(empInfoEntity.getOrganize());
        doData.setWorkPlace(empInfoEntity.getWorkplace() == null ? "" : empInfoEntity.getWorkplace());
        doData.setCompany(empInfoEntity.getCompany() == null ? "" : empInfoEntity.getCompany());
        doData.setEmpType(empInfoEntity.getEmpType() == null ? new DictSimple() : empInfoEntity.getEmpType());
        doData.setContractSettingType(
                empInfoEntity.getContractType() == null ? new DictSimple() : empInfoEntity.getContractType());
        log.info("contractSettingType：" + doData.getContractSettingType().toString());
        // certificate.setEmpId(doData.getEmpId());
        // certificate.update(certificate);
        if (StringUtils.isNotEmpty(vo.getFormId())) {
            FormDefDto data = formFeignClient.getFormDefById(vo.getFormId()).getData();
            FormDataDto formDataDto = new FormDataDto();
            formDataDto.setId(data.getId());
            formDataDto.setTargetId(data.getTarget());
            formDataDto.setPropertiesMap(vo.getFormDataMap());
            String dataid = doData.getFormData();
            if (StringUtils.isEmpty(vo.getBid())) {
                dataid = formFeignClient.saveFormData(vo.getFormId(), formDataDto).getData();
                if (StringUtils.isEmpty(dataid)) {
                    throw new ServerException("Form data saving failed");
                }

            } else {

                CeritificateAndEmpDo ceritificateAndEmpDo = this.ceritificateAndEmpDo.selectById(vo.getBid());
                dataid = ceritificateAndEmpDo.getFormData();
                FormDataDto formDataDto1 = new FormDataDto();
                formDataDto1.setId(dataid);
                formDataDto1.setPropertiesMap(vo.getFormDataMap());
                Result result = formFeignClient.update(vo.getFormId(), formDataDto1);
                log.info(result.toString());
                if (result.getCode() != 0) {
                    throw new ServerException(result.getMsg());
                }

            }
            doData.setFormData(dataid);

        }
        doData.checkStatusAndUseStatus();
        if (StringUtils.isEmpty(vo.getBid())) {

            return doData.save(doData) + "/员工状态" + empInfoEntity.getEmpStatus().getText();
        } else {
            CeritificateAndEmpDo entity = ceritificateAndEmpDo.selectById(vo.getBid());
            doData.setProBid(entity.getProBid());
            return doData.update(doData) + "/员工状态" + empInfoEntity.getEmpStatus().getText();
        }
    }

    /**
     * 处理证书使用状态
     */
    private void initUseStatus(CeritificateAndEmpDo dto) {
        dto.checkStatusAndUseStatus();
    }

    /**
     * 检查证书是否被项目使用
     */
    private boolean check(CeritificateAndEmpDto vo) {
        CeritificateAndEmpDo empDo = ceritificateAndEmpDo.selectById(vo.getBid());
        // TODO: 2025/7/28 根据bid查询是否有项目引用
        if (StringUtils.isNotEmpty(empDo.getProBid())) {
            Optional<CertificateProjectDo> projectDo = CertificateProjectDo.findById(empDo.getProBid());
            if (ObjectUtil.isNotEmpty(projectDo.get())) {
                return false;
            }
        }
        return true;
    }

    /**
     * 删除
     */
    public void delete(CeritificateAndEmpDto vo) {
        PreCheck.preCheckArgument(StringUtils.isEmpty(vo.getBid()), "bid为空");
        PreCheck.preCheckArgument(!check(vo), "该证书已被项目上使用，不可删除");
        CeritificateAndEmpDo data = BeanUtil.convert(vo, CeritificateAndEmpDo.class);
        data.delete(data);
    }

    /**
     * 详情
     */
    public List<CeritificateAndEmpDto> getById(CeritificateAndEmpQueryDto vo) {
        Map<String, Object> map = Maps.newHashMap();
        // PreCheck.preCheckArgument(StringUtils.isEmpty(vo.getBid()), "bid为空");
        PreCheck.preCheckArgument(StringUtils.isEmpty(vo.getEmpId()), "员工id为空");
        List<CeritificateAndEmpDo> doList = ceritificateAndEmpRepository.queryList(vo);
        Sequences.sequence(doList).forEach(CeritificateAndEmpDo::checkStatusAndUseStatus);
        List<CeritificateAndEmpDto> list = ObjectConvertUtil.convertList(doList,
                CeritificateAndEmpDto.class,
                (t, v1) -> {
                    v1.setI18nName(FastjsonUtil.toObject(t.getI18nName(), Map.class));
                });
        convertI18n(list);
        List<String> sortList = CertificateDo.sortList().stream().map(AbstractData::getBid)
                .collect(Collectors.toList());
        list.sort(Comparator.comparingInt(data -> sortList.indexOf(data.getCeritifiCateBid())));

        if (StringUtils.isNotEmpty(vo.getCeritificateStatus())) {
            Map<String, List<CeritificateAndEmpDto>> statusGroup = Sequences.sequence(list)
                    .toMap(c -> c.getStatus().getValue());
            List<CeritificateAndEmpDto> sortCertificateList = new ArrayList<>();
            sortCertificateList.addAll(statusGroup.get(CertificateStatusEnum.VALID.getValue()));
            List<CeritificateAndEmpDto> invalidList = statusGroup.get(CertificateStatusEnum.INVALID.getValue());
            invalidList.sort(Comparator.comparingLong(data -> -(StringUtils.isEmpty(data.getExpireTime()) ? DateUtil.MAX_TIMESTAMP : Long.valueOf(data.getExpireTime()))));
            sortCertificateList.addAll(invalidList);
            return sortCertificateList;
        } else {
            List<CeritificateAndEmpDto> collect = list.stream().filter(t -> t.getStatus().getValue().equals("0"))
                    .collect(Collectors.toList());
            return collect;
        }
    }

    private void convertI18n(List<CeritificateAndEmpDto> list) {
        List<CertificateDo> certificateDos = certificateDo.all(new CertificateQueryDto());
        Map<String, CertificateDo> certificateI18nMap = certificateDos.stream()
                .collect(Collectors.toMap(AbstractData::getBid,
                        obj -> obj, (a, b) -> a));

        List<CertificateTypeDo> certificateTypeDos = certificateTypeDo.all(new CertificateTypeQueryDto());
        Map<String, CertificateTypeDo> typeI18nMap = certificateTypeDos.stream()
                .collect(Collectors.toMap(AbstractData::getBid, obj -> obj, (a, b) -> a));

        for (CeritificateAndEmpDto emp : list) {
            CertificateTypeDo typeDo = typeI18nMap.get(emp.getTypeBid());
            emp.setTypeName(LangUtil.getCurrentLangVal(FastjsonUtil.toObject(typeDo.getI18nName(), Map.class)));

            CertificateTypeDo subTypeDo = typeI18nMap.get(emp.getTypeSubBid());
            emp.setTypeSubName(LangUtil.getCurrentLangVal(FastjsonUtil.toObject(subTypeDo.getI18nName(), Map.class)));

            CertificateDo certificateDo = certificateI18nMap.get(emp.getCeritifiCateBid());
            emp.setCeritifiCateName(
                    LangUtil.getCurrentLangVal(FastjsonUtil.toObject(certificateDo.getI18nName(), Map.class)));

        }
    }

    @Deprecated
    private void checkProUse(boolean flag, List<CertificateUsageRecordDo> items) {
        List<CertificateUsageRecordDo> list = items.stream()
                .sorted(Comparator.comparing(CertificateUsageRecordDo::getEndDate).reversed())
                .collect(Collectors.toList());
        // 取最后一个时间比较现在时间，判断是否被使用
        if (CollectionUtils.isNotEmpty(list)) {
            if (list.get(0).getEndDate() < System.currentTimeMillis()) {
                flag = true;
            }
        }

    }

    /**
     * 作废
     */
    public void discard(CeritificateAndEmpDto vo) {
        PreCheck.preCheckArgument(StringUtils.isEmpty(vo.getBid()), "bid为空");
        PreCheck.preCheckArgument(!check(vo), "该证书已被项目上使用，不可作废");
        CeritificateAndEmpDo entity = ceritificateAndEmpDo.selectById(vo.getBid());
        initUseStatus(entity);
        entity.setExpireTime(vo.getExpireTime());
        entity.setRemake(vo.getRemake());
        entity.update(entity);
    }

    public List<CertificateAndEmpExcelVo> selectExportRecordPage(CeritificateAndEmpQueryDto dto) {
        dto.setPageSize(-1);
        dto.setPageNo(1);
        List<CeritificateAndEmpDto> doList = tablePage(dto).getItems();
        List<CertificateAndEmpExcelVo> list = BeanUtil.convertList(doList, CertificateAndEmpExcelVo.class);
        list.forEach(t -> {
            t.setStatustxt(t.getStatus().getText());
            t.setCertificateStatus(t.getUseStatus().getText());
            t.setIsRegisterTxt(t.getIsRegister().getText());
            t.setIssueAtTxt(t.getIssueAt().getText());
            t.setIsuseTxt(t.getIsuse().getText());
            t.setAccessProvinceTxt(t.getAccessProvince().getText());
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
            if (t.getRegisterTime() != null) {
                Date registerTime = new Date(Long.valueOf(t.getRegisterTime()));
                t.setRegisterTime(sdf.format(registerTime));
            }
            if (t.getExpireTime() != null) {
                Date expireTime = new Date(Long.valueOf(t.getExpireTime()));
                t.setExpireTime(sdf.format(expireTime));
            }
            if (t.getAcquiredTime() != null) {
                Date date = new Date(t.getAcquiredTime());
                t.setAcquiredTimeTxt(sdf.format(date));
            }
        });
        return list;
    }

    public List<TagProperty> installContractRecordExportProperty() {
        List<TagProperty> list = new ArrayList<>();
        addTagPropertyToList(list, "workNo", "工号", 3);
        addTagPropertyToList(list, "workName", "姓名", 4);
        addTagPropertyToList(list, "typeName", "证书类别", 12);
        addTagPropertyToList(list, "ceritifiCateName", "证书名称", 15);
        addTagPropertyToList(list, "ceritifiCateCode", "证书编号", 16);
        addTagPropertyToList(list, "isRegisterTxt", "是否注册", 17);
        addTagPropertyToList(list, "registerTime", "注册日期", 18);
        addTagPropertyToList(list, "registrationNo", "注册号", 18);
        addTagPropertyToList(list, "acquiredTimeTxt", "取得日期", 19);
        addTagPropertyToList(list, "expireTime", "失效日期", 20);
        addTagPropertyToList(list, "specialty", "专业", 21);
        addTagPropertyToList(list, "issueAuthority", "签发机构", 22);
        addTagPropertyToList(list, "issueAtTxt", "签发地", 23);
        addTagPropertyToList(list, "accessProvinceTxt", "入省备案", 25);
        addTagPropertyToList(list, "remake", "备注", 26);
        addTagPropertyToList(list, "isuseTxt", "限制使用", 27);
        addTagPropertyToList(list, "certificateStatus", "证书使用状态", 28);
        addTagPropertyToList(list, "statustxt", "状态", 29);

        return list;
    }

    public void addTagPropertyToList(List<TagProperty> list, String property, String propertyTxt, int order) {
        list.add(new TagProperty(property, propertyTxt, order));
    }

    public PageResult<CertificateUsageRecordShowDto> getDetail(CeritificateAndEmpQueryDto vo) {
        PreCheck.preCheckArgument(StringUtils.isEmpty(vo.getProBid()), "该证书尚未有项目登记");
        PageResult<CertificateUsageRecordDo> page = CertificateUsageRecordDo.pageByEmpCertificate(vo.getBid());
        if (page.getItems().isEmpty()) {
            return new PageResult(
                    new ArrayList(), page.getPageNo(), page.getPageSize(), page.getTotal());
        }

        List<String> proIds = Sequences.sequence(page.getItems()).map(CertificateUsageRecordDo::getProjectId).toList();
        List<CertificateProjectDo> projectDoList = CertificateProjectDo.listById(proIds);

        List<CertificateUsageRecordShowDto> list = beanConvert(page.getItems(), CertificateUsageRecordShowDto.class);
        list.forEach(t -> {
            CertificateDo certificateDo = this.certificateDo.selectById(t.getCertificate());
            t.setCertificate(certificateDo.getName());
            t.setProjectName(Sequences.sequence(projectDoList).find(p -> p.getBid().equals(t.getProjectId()))
                    .map(CertificateProjectDo::getProjectName).getOrElse(""));
        });
        return new PageResult(
                list, page.getPageNo(), page.getPageSize(), page.getTotal());
    }

    private List<CertificateUsageRecordShowDto> beanConvert(List<CertificateUsageRecordDo> items,
            Class<CertificateUsageRecordShowDto> T) {
        List<CertificateUsageRecordShowDto> list = BeanUtil.convertList(items, T);
        list.forEach(t -> {
            CertificateProjectDto certificateProject = SpringUtil.getBean(CertificateProjectService.class)
                    .getCertificateProject(t.getProjectId());
            if (ObjectUtil.isNotEmpty(certificateProject)) {
                t.setProjectName(t.getProjectName());
            }
            EmpWorkInfoVo empInfoEntity = getEntity(t.getEmpId());
            t.setEmpName(empInfoEntity.getName());
            t.setEmpWorkNo(empInfoEntity.getWorkno());
        });
        return list;
    }

    public List<Map<String, Object>> dropList(String bid) {
        List<Map<String, Object>> finalList = Lists.list();

        if (StringUtils.isNotEmpty(bid)) {
            CertificateTypeQueryDto dto = new CertificateTypeQueryDto();
            dto.setLevel(2);
            dto.setStatus("0");
            dto.setPBid(bid);
            return getMaps(finalList, dto);
        } else {
            CertificateTypeQueryDto dto = new CertificateTypeQueryDto();
            dto.setLevel(1);
            dto.setStatus("0");
            return getMaps(finalList, dto);
        }
    }

    private List<Map<String, Object>> getMaps(List<Map<String, Object>> finalList, CertificateTypeQueryDto dto) {
        HttpServletRequest httpServletRequest = WebUtil.getRequest();
        String header = httpServletRequest.getHeader("Accept-Language");
        List<CertificateTypeDo> list = SpringUtil.getBean(CertificateTypeDo.class).selectList(dto);

        list.forEach(t -> {
            Map map1 = FastjsonUtil.toObject(t.getI18nName(), Map.class);
            Map<String, Object> map = new HashMap<>();
            map.put("name", map1.get(header) == null ? map1.get("default") : map1.get(header));
            map.put("code", t.getCode());
            map.put("bid", t.getBid());
            finalList.add(map);
        });
        return finalList;
    }

    public CeritificateAndEmpDto selectByid(CeritificateAndEmpQueryDto vo) {
        CeritificateAndEmpDo entities = SpringUtil.getBean(ICeritificateAndEmpRepository.class).selectById(vo.getBid(),
                CeritificateAndEmpDo.IDENTIFIER);
        CeritificateAndEmpDto convert = ObjectConvertUtil.convert(entities, CeritificateAndEmpDto.class,
                (t, v1) -> {
                    EmpWorkInfoVo entity = getEntity(t.getEmpId());
                    v1.setWorkName(entity.getName());
                    v1.setWorkNo(entity.getWorkno());
                    v1.setI18nName(FastjsonUtil.toObject(t.getI18nName(), Map.class));
                });
        return convert;
    }

    public Map<String, Object> getByEmpId(String empId) {
        Map<String, Object> map = new HashMap<>();
        CeritificateAndEmpQueryDto data = new CeritificateAndEmpQueryDto();
        data.setEmpId(empId);
        // 员工个人信息
        List<EmpInfoEntity> entityList = SpringUtil.getBean(IEmpInfoRepository.class).queryList(data,
                System.currentTimeMillis());

        EmpInfoEntity empInfoEntity = entityList.get(0);
        // 任职信息
        EmpWorkInfoVo empWorkInfo = SpringUtil.getBean(MasterDataEmpInfoFeign.class)
                .loadEmpWorkInfo(empId, System.currentTimeMillis()).getData();

        map.putAll(convertObjectToMap(empWorkInfo));
        map.putAll(convertObjectToMap(empInfoEntity));
        CertificateSettingDo settingDo = new CertificateSettingDo();
        settingDo.setTypeCode("emp");
        List<CertificateSettingGetResultDto> maps = certificateSettingService.get(settingDo);
        List<String> keys = maps.stream()
                .flatMap(t -> {
                    List<SettingDto> settingDtos = t.getProperties();
                    if (settingDtos != null) {
                        return settingDtos.stream().map(SettingDto::getProperty);
                    } else {
                        return Stream.empty();
                    }
                })
                .collect(Collectors.toList());
        Map<String, Object> collect = map.entrySet().stream().filter(t -> keys.contains(t.getKey()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        return collect;

    }

    /**
     * 证书在项目中被锁定，绑定项目
     *
     * @param bid
     * @param projectId
     */
    public void projectAcquired(String bid, String projectId) {
        CeritificateAndEmpDo entity = ceritificateAndEmpDo.selectById(bid);
        entity.setProBid(projectId);
        entity.setUseStatus(CertificateUseStatusEnum.IN_USE.toEnumSimple());
        entity.update(entity);
    }

    /**
     * 证书在项目中解锁，取消绑定
     *
     * @param bid
     */
    public void projectReset(String bid) {
        CeritificateAndEmpDo entity = ceritificateAndEmpDo.selectById(bid);
        entity.setProBid(null);
        entity.setStatus(CertificateUseStatusEnum.AVAILABLE.toEnumSimple());
        entity.update(entity);
    }

    public String judge(CeritificateAndEmpDto vo) {
        CeritificateAndEmpQueryDto dto = new CeritificateAndEmpQueryDto();
        dto.setEmpId(vo.getEmpId());
        dto.setCeritifiCateName(vo.getCeritifiCateName());
        // dto.setRegisterTime(vo.getRegisterTime());
        List<CeritificateAndEmpDo> doList = SpringUtil.getBean(ICeritificateAndEmpRepository.class).queryList(dto)
                .stream()
                .filter(t -> t.getExpireTime() == null || Long.valueOf(t.getExpireTime()) >= System.currentTimeMillis())
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(doList)) {
            return "字段重复";
        } else {
            return null;
        }
    }

    // 是否限制使用，证书使用状态进行判断
    @Deprecated
    private void statusSets(List<CeritificateAndEmpDto> list) {
        if (CollectionUtils.isNotEmpty(list)) {
            // list.forEach(CeritificateAndEmpDo::setStatusForDto);
        }
    }

    @Deprecated
    private void statusSets(CeritificateAndEmpDto t) {
        // CeritificateAndEmpDo.setStatusForDto(t);
    }

    public Option<CeritificateAndEmpDo> loadActiveCertificateByEmp(String empId, String certificate) {
        return Sequences.sequence(ceritificateAndEmpRepository.listByEmpId(empId, certificate)).headOption();
    }

    private Map<String, Object> convertObjectToMap(Object obj) {
        try {
            return (com.caidaocloud.certificate.service.certificate.domain.base.util.BeanUtil.bean2map(obj));
        } catch (Exception e) {
            throw new ServerException("object to map error");
        }
    }

    /**
     * 用于手工刷新状态
     *
     * @param dto
     * @return
     */
    public boolean flush(CeritificateAndEmpDto dto) {
        CeritificateAndEmpQueryDto dto1 = new CeritificateAndEmpQueryDto();
        dto1.setBid(dto.getBid());
        CeritificateAndEmpDto empDto = selectByid(dto1);
        CeritificateAndEmpDo doData = BeanUtil.convert(empDto, CeritificateAndEmpDo.class);
        initUseStatus(doData);
        doData.update(doData);
        return true;
    }

    // 初始化证书数量
    public void initCertificateNums() {
        int pageNo = 1;
        while (true) {
            List<CeritificateAndEmpDo> page = queryPage(new CeritificateAndEmpQueryDto(), 1000, pageNo++);
            Map<String, List<CeritificateAndEmpDo>> collect = page.stream()
                    .collect(Collectors.groupingBy(CeritificateAndEmpDo::getEmpId));
            if (page.isEmpty()) {
                break;
            }
            for (Map.Entry<String, List<CeritificateAndEmpDo>> stringListEntry : collect.entrySet()) {
                List<CeritificateAndEmpDo> value = stringListEntry.getValue();
                Integer certificateNum = 0;
                for (CeritificateAndEmpDo t1 : value) {
                    if (t1.isDeleted()) {
                        continue;
                    }
                    if (t1.getExpireTime() == null) {

                    } else {
                        Date date = new Date(Long.parseLong(t1.getExpireTime()));
                        Date nowData = new Date(System.currentTimeMillis());
                        if (date.before(nowData)) {
                            continue;
                        }
                    }
                    certificateNum++;
                }
                for (CeritificateAndEmpDo empDo : value) {
                    empDo.setCertifiCateNums(certificateNum);
                    empDo.update(empDo);
                }

            }
        }
    }

    // 用于xxxjob 用
    public List<CeritificateAndEmpDo> queryPage(CeritificateAndEmpQueryDto dto, Integer pageSize, Integer pageNo) {
        return ceritificateAndEmpRepository.queryPage(dto, pageSize, pageNo).getItems();
    }

    public void test() {

        try {
            List<WfMetaFunFormFieldDto> wfMetaFunFormFieldDtos = fileTableInit(CeritificateAndEmpHistoryDo.code);
            doInitWfRegister(wfMetaFunFormFieldDtos);
            WfFunctionConfiguration.getFunFieldMap().putIfAbsent("CERTIFICATE-EMP",
                    ImmutableMap.copyOf(wfMetaFunFormFieldDtos.stream()
                            .collect(Collectors.toMap(e -> e.getCode(), e -> e, (v1, v2) -> v2))));
            log.info("WfMetaFunFormFiel init success ");
        } finally {
        }
    }

    private void doInitWfRegister(List<WfMetaFunFormFieldDto> wfMetaFunFormFieldDtos) {
        // 员工证书申请
        WfMetaFunDto dto = new WfMetaFunDto("员工证书申请", "CERTIFICATE-EMP",
                WfFunctionPageJumpType.RELATIVE_PATH, "",
                "caidaocloud-certificate-service",
                "", "/api/certificate/v1/emp/getById", "", wfMetaFunFormFieldDtos);
        iWfRegisterFeign.registerFunction(dto);

        registerCallback("CERTIFICATE-EMP", "回调失败", "CERTIFICATE-FAIL", "/api/certificate/v1/history/fail");
        WfMetaCallbackDto wmc;

        registerCallback("CERTIFICATE-EMP", "回调成功", "CERTIFICATE-SUCCESS", "/api/certificate/v1/history/success");
        WfMetaCallbackDto wmcc;

    }

    private void registerCallback(String newSignFunCode, String name, String code, String apiPath) {
        WfMetaCallbackDto wmc = new WfMetaCallbackDto(name, code, Lists.list(newSignFunCode),
                "",
                apiPath,
                "caidaocloud-certificate-service",
                "",
                WfCallbackTypeEnum.RELATIVE_PATH,
                WfCallbackTimeTypeEnum.NOW);
        iWfRegisterFeign.registerCallback(wmc);
    }

    public List<WfMetaFunFormFieldDto> fileTableInit(String formCode) {
        Result<FormDefDto> data = SpringUtil.getBean(FormFeignClient.class).getFormDefByCode(formCode);
        if (ObjectUtil.isEmpty(data) || ObjectUtil.isEmpty(data.getData())) {
            return new ArrayList<>();
        }
        List<WfMetaFunFormFieldDto> formFields = Lists.list(
                new WfMetaFunFormFieldDto("typeName", "证书类型", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("typeSubName", "证书子类型", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("ceritifiCateName", "证书名称", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("ceritifiCateCode", "证书编码", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("expireTime", "失效日期", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("registerTime", "注册日期", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("registrationNo", "注册号", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("i18nName", "专业国际化", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("issueAt", "签发地", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("accessProvince", "入省备案", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("isuse", "是否限制使用", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("isRegister", "是否注册", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("remake", "备注", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("annex", "附件", WfFieldDataTypeEnum.Text));
        List<FormDefMetadataDto> properties = data.getData().getProperties();
        properties.stream().forEach(t -> {
            formFields.add(new WfMetaFunFormFieldDto(t.getProperty(), t.getName(), WfFieldDataTypeEnum.Text));
        });

        return formFields;
    }

    public PageResult<CeritificateAndEmpDto> tablePage(CeritificateAndEmpQueryDto vo) {
        HttpServletRequest request = WebUtil.getRequest();
        String header = request.getHeader("Accept-Language");

        if (StringUtils.isNotEmpty(vo.getApiType())) {
            if (vo.getApiType().equals("2")) {
                // vo.setUseStatus("0");
                vo.setCeritificateStatus("0");
                vo.setEmpStatus("0");
            }
        }
        // if (StringUtils.isEmpty(vo.getEmpStatus())) {
        // vo.setEmpStatus("0");
        // }
        PageResult<CeritificateAndEmpDo> page = ceritificateAndEmpRepository.queryTablePage(vo, vo.getPageSize(),
                vo.getPageNo());
        return new PageResult(convertTableVo(page.getItems(), header), vo.getPageNo(), vo.getPageSize(),
                page.getTotal());
    }

    public void flush2(CeritificateAndEmpDo data) {
        CeritificateAndEmpDo ceritificateAndEmpDo = ObjectConverter.convert(data, CeritificateAndEmpDo.class);
        initUseStatus(data);
        if (ceritificateAndEmpDo.getStatus() != null && Objects.equals(ceritificateAndEmpDo.getStatus()
                .getValue(), data.getStatus().getValue()) && ceritificateAndEmpDo.getUseStatus() != null
                && Objects.equals(ceritificateAndEmpDo.getUseStatus()
                        .getValue(), data.getUseStatus().getValue())) {
            return;
        }
        data.update(data);
    }

    public PageResult<CeritificateAndEmpDto> loadAvailableEmployees(AvailableEmpQueryDto queryDto) {
        List<String> rangeCertificate = new ArrayList<>();
        if (queryDto.getStartTime() != null && queryDto.getEndTime() != null) {
            List<CertificateUsageRecordDo> recordDoList = CertificateProjectDo
                    .listRecordByRange(queryDto.getCeritifiCateBid(), queryDto.getStartTime(), queryDto.getEndTime());
            rangeCertificate = Sequences.sequence(recordDoList).map(CertificateUsageRecordDo::getEmpCertificate)
                    .toList();
            queryDto.setExcludeBid(rangeCertificate);
        }
        queryDto.setCeritificateStatus(null);
        return tablePage(queryDto);
    }
}
