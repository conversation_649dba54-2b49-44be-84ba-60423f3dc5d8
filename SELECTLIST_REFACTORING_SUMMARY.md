# CertificateSettingService.selectList 方法重构总结

## 重构目标
将 `CertificateSettingService.selectList()` 方法重构为两个专门的方法，根据业务逻辑分别返回不同类型的集合，并更新调用方直接使用对应的子方法。

## 重构内容

### 1. 原方法分析
原 `selectList()` 方法根据 `typeCode` 参数的值执行不同的逻辑：
- 当 `typeCode` 不等于 "msg" 时：返回 `List<SettingDto>`
- 当 `typeCode` 等于 "msg" 时：返回 `List<CertificateSettingDo>`

### 2. 重构后的方法结构

#### 保留的原方法
```java
public Object selectList(CertificateSettingDo dto) {
    if (!dto.getTypeCode().equals("msg")) {
        return selectSettingDtoList(dto);
    }
    return selectCertificateSettingDoList(dto);
}
```
- **用途**: 保持向后兼容性
- **返回类型**: `Object`（为了兼容现有调用）

#### 新增方法1：selectSettingDtoList
```java
public List<SettingDto> selectSettingDtoList(CertificateSettingDo dto)
```
- **用途**: 专门处理非msg类型的查询
- **返回类型**: `List<SettingDto>`
- **业务逻辑**: 
  - 查询CertificateSettingDo列表
  - 转换为SettingDto列表并排序
  - 如果为空，则根据typeCode构建默认数据

#### 新增方法2：selectCertificateSettingDoList
```java
public List<CertificateSettingDo> selectCertificateSettingDoList(CertificateSettingDo dto)
```
- **用途**: 专门处理msg类型的查询
- **返回类型**: `List<CertificateSettingDo>`
- **业务逻辑**: 直接调用底层查询方法

### 3. 调用方更新

#### CertificateExpireTask
**原代码**:
```java
CertificateSettingDo data = new CertificateSettingDo();
data.setTypeCode("msg");
List<CertificateSettingDo> settingDtos = (List<CertificateSettingDo>)certificateSettingService.selectList(data);
```

**重构后**:
```java
CertificateSettingDo data = new CertificateSettingDo();
data.setTypeCode("msg");
List<CertificateSettingDo> settingDtos = certificateSettingService.selectCertificateSettingDoList(data);
```

#### QualifiedPerService
**原代码**:
```java
CertificateSettingDo certificateSettingDo = new CertificateSettingDo();
certificateSettingDo.setTypeCode("qua");
Object d = certificateSettingService.selectList(certificateSettingDo);
List<SettingDto> settingDtos = FastjsonUtil.toArrayList(FastjsonUtil.toJson(d), SettingDto.class);
```

**重构后**:
```java
CertificateSettingDo certificateSettingDo = new CertificateSettingDo();
certificateSettingDo.setTypeCode("qua");
List<SettingDto> settingDtos = certificateSettingService.selectSettingDtoList(certificateSettingDo);
```

### 4. 保持不变的调用
以下调用保持不变，因为无法明确判断业务类型：
- `CertificateSettingController.selectList()` - 通过API调用，typeCode由前端传入
- `CeritificateAndEmpDynamicService.dynamicColumn()` - 使用枚举类型调用

## 重构优势

### 1. 类型安全
- **消除强制类型转换**: 调用方不再需要进行 `(List<CertificateSettingDo>)` 类型转换
- **编译时类型检查**: 返回类型明确，编译器可以进行类型检查
- **减少ClassCastException风险**: 避免运行时类型转换异常

### 2. 代码可读性
- **方法名明确**: `selectSettingDtoList` 和 `selectCertificateSettingDoList` 清楚表达返回类型
- **业务意图清晰**: 调用方可以根据业务需求选择合适的方法
- **减少JSON序列化**: QualifiedPerService 不再需要通过JSON转换来获取正确类型

### 3. 性能优化
- **避免不必要的序列化**: QualifiedPerService 中移除了 `FastjsonUtil.toArrayList(FastjsonUtil.toJson(d), SettingDto.class)` 的转换
- **减少反射操作**: 直接返回强类型对象，避免运行时类型检查

### 4. 维护性
- **单一职责**: 每个方法只处理一种业务场景
- **易于扩展**: 如果需要添加新的业务逻辑，可以独立修改对应方法
- **向后兼容**: 原 `selectList` 方法保留，不影响现有调用

## 测试验证

创建了 `CertificateSettingServiceTest` 测试类，包含以下测试用例：
1. `testSelectSettingDtoList()` - 验证非msg类型查询返回SettingDto列表
2. `testSelectCertificateSettingDoList()` - 验证msg类型查询返回CertificateSettingDo列表
3. `testSelectListCompatibility()` - 验证原方法的向后兼容性

## 注意事项

1. **向后兼容性**: 原 `selectList` 方法保留，确保现有API不受影响
2. **业务判断**: 只有在能明确判断业务类型时才直接调用子方法
3. **错误处理**: 新方法继承了原方法的错误处理逻辑

## 后续建议

1. **逐步迁移**: 建议其他调用方在明确业务类型后逐步迁移到新方法
2. **API文档更新**: 更新相关API文档，说明新方法的使用场景
3. **监控观察**: 观察重构后的性能表现和错误率
4. **考虑废弃**: 在所有调用方迁移完成后，可以考虑标记原方法为 `@Deprecated`
